// This is a migration script to fix base category values
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function fixBaseCategories() {
  try {
    // Update any NULL categories to 'OTHER'
    await prisma.$executeRaw`UPDATE "Base" SET "category" = 'OTHER' WHERE "category" IS NULL`;

    // Find any bases with incompatible category values
    const basesWithIssues = await prisma.base.findMany({
      where: {
        NOT: {
          category: {
            in: ['WAR', 'FARMING', 'TROPHY', 'HYBRID', 'OTHER'],
          },
        },
      },
    });

    console.log(
      `Found ${basesWithIssues.length} bases with invalid category values.`
    );

    // Update each base with an incompatible category value
    for (const base of basesWithIssues) {
      let targetCategory = 'OTHER';

      // Try to map existing category values to enum values (case insensitive)
      const currentCategory = base.category?.toUpperCase();
      if (['WAR', 'FARMING', 'TROPHY', 'HYBRID'].includes(currentCategory)) {
        targetCategory = currentCategory;
      }

      await prisma.base.update({
        where: { id: base.id },
        data: { category: targetCategory },
      });
      console.log(
        `Updated base ${base.id} from "${base.category}" to "${targetCategory}"`
      );
    }

    console.log('All category values have been fixed.');
  } catch (error) {
    console.error('Error fixing categories:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixBaseCategories();
