generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model User {
  id               String    @id @default(cuid())
  name             String?
  email            String?   @unique
  emailVerified    DateTime?
  image            String?
  isActive         Boolean   @default(false)
  stripeCustomerId String?   @unique
  accounts         Account[]
  sessions         Session[]
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Base {
  id             String       @id @default(uuid())
  createdAt      DateTime     @default(now())
  views          Int          @default(0)
  imageUrl       String
  level          Int
  type           BaseType
  baseLink       String?
  title          String?
  sortOrder      Int          @default(0)
  category       BaseCategory @default(OTHER)
  downloadCount  Int          @default(0)
  visits         BaseVisit[]
  downloadLogs   BaseDownload[]
}

model CategoryVisit {
  id     String   @id @default(uuid())
  type   BaseType
  level  Int
  visits Int      @default(0)

  @@unique([type, level])
}

model BaseVisit {
  id        String       @id @default(uuid())
  baseId    String
  base      Base         @relation(fields: [baseId], references: [id])
  createdAt DateTime     @default(now())
  visitorIp String?
  baseType  String       // 'town-hall' or 'builder-hall'
  level     Int
  category  String?      // WAR, FARMING, TROPHY, HYBRID, OTHER

  @@index([baseId])
  @@index([baseType, level])
}

model BaseDownload {
  id        String       @id @default(uuid())
  baseId    String
  base      Base         @relation(fields: [baseId], references: [id])
  createdAt DateTime     @default(now())
  visitorIp String?

  @@index([baseId])
}

enum BaseType {
  BUILDER_HALL
  TOWN_HALL
}

enum BaseCategory {
  WAR
  FARMING
  TROPHY
  HYBRID
  OTHER
}
