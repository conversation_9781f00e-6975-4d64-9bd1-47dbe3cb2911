import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

const TH_LEVELS = Array.from({ length: 17 }, (_, i) => i + 1); // 1-17
const BH_LEVELS = Array.from({ length: 10 }, (_, i) => i + 1); // 1-10
const IMAGE_URL = '/images/TownHalls/th3_farm_113.jpg';
const BASE_LINK =
  'https://clashofclans-layouts.com/plans/th_16/defence_34.html';

async function main() {
  // Town Hall
  for (const level of TH_LEVELS) {
    const count = Math.floor(Math.random() * 12) + 8; // 8-19
    for (let i = 0; i < count; i++) {
      await prisma.base.create({
        data: {
          type: 'TOWN_HALL',
          level,
          title: `TH${level} Farming Base #${i + 1}`,
          imageUrl: IMAGE_URL,
          baseLink: BASE_LINK,
          views: Math.floor(Math.random() * 100),
          sortOrder: 0,
          category: 'FARMING',
        },
      });
    }
  }
  // Builder Hall
  for (const level of BH_LEVELS) {
    const count = Math.floor(Math.random() * 13) + 8; // 8-20
    for (let i = 0; i < count; i++) {
      await prisma.base.create({
        data: {
          type: 'BUILDER_HALL',
          level,
          title: `BH${level} Farming Base #${i + 1}`,
          imageUrl: IMAGE_URL,
          baseLink: BASE_LINK,
          views: Math.floor(Math.random() * 100),
          sortOrder: 0,
          category: 'FARMING',
        },
      });
    }
  }
  console.log('Seeded mock bases for all levels.');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
