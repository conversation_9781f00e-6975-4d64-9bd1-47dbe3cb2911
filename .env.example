# Duplicate this to .env

# Database
DATABASE_URL='postgresql://username:password@host:port/database' ## required for prisma - create free PostgreSQL database: https://dashboard.render.com/new/database

# Authentication
AUTH_USERNAME='your_username'
AUTH_EMAIL='<EMAIL>'
AUTH_PASSWORD='your_secure_password'
JWT_SECRET='your_jwt_secret' ## generate one here: https://generate-secret.vercel.app/32

# App Configuration
APP_URL='https://your-app-url.com' ## required for next-sitemap

# OAuth Providers
GITHUB_ID='your_github_client_id' ## required for next-auth
GITHUB_SECRET='your_github_client_secret' ## required for next-auth
GOOGLE_CLIENT_ID='your_google_client_id.apps.googleusercontent.com'
GOOGLE_CLIENT_SECRET='your_google_client_secret'
FACEBOOK_CLIENT_ID='your_facebook_client_id'
FACEBOOK_CLIENT_SECRET='your_facebook_client_secret'

# NextAuth Configuration
NEXTAUTH_SECRET='your_nextauth_secret' ## required for next-auth - generate one here: https://generate-secret.vercel.app/32
NEXTAUTH_URL='http://localhost:3000' ## Only required for localhost
