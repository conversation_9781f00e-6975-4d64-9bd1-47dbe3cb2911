name: Code check

on:
  pull_request:
    branches:
      - main

jobs:
  lint:
    name: <PERSON><PERSON><PERSON>, Typescript, prettier and test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Install dependencies
        run: npm install
      - name: ESLint
        run: npm run lint
      - name: Type check
        run: npm run typecheck
      - name: Prettier
        run: npm run format:check
      - name: Tests
        run: npm run test
