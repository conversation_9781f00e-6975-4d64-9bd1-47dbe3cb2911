name: Discord Commit Notify

on:
  push:
    branches: [main]
  pull_request:
    types: [opened, closed, synchronize]

jobs:
  notify:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Get commit details
        id: commit-details
        run: |
          if [[ "${{ github.event_name }}" == "push" ]]; then
            echo "::set-output name=title::[COC-Bases]:development/New commit"
            echo "::set-output name=message::${{ github.event.head_commit.message }}"
            echo "::set-output name=url::${{ github.event.head_commit.url }}"
            echo "::set-output name=author::${{ github.event.head_commit.author.name }}"
            echo "::set-output name=avatar::https://github.com/${{ github.event.head_commit.author.username }}.png"
          else
            echo "::set-output name=title::Pull Request ${{ github.event.action }}"
            echo "::set-output name=message::${{ github.event.pull_request.title }}"
            echo "::set-output name=url::${{ github.event.pull_request.html_url }}"
            echo "::set-output name=author::${{ github.event.pull_request.user.login }}"
            echo "::set-output name=avatar::${{ github.event.pull_request.user.avatar_url }}"
          fi

      - name: Send notification to Discord
        env:
          DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK }}
        run: |
          # Format commit message for JSON (escape quotes and newlines)
          MESSAGE=$(echo '${{ steps.commit-details.outputs.message }}' | sed 's/"/\\"/g' | sed ':a;N;$!ba;s/\n/\\n/g')

          curl -H "Content-Type: application/json" \
          -X POST \
          -d "{
            \"embeds\": [{
              \"title\": \"${{ steps.commit-details.outputs.title }}\",
              \"description\": \"${MESSAGE}\",
              \"url\": \"${{ steps.commit-details.outputs.url }}\",
              \"color\": 5814783,
              \"author\": {
                \"name\": \"${{ steps.commit-details.outputs.author }}\",
                \"icon_url\": \"${{ steps.commit-details.outputs.avatar }}\"
              },
              \"thumbnail\": {
                \"url\": \"https://raw.githubusercontent.com/farhanf7n/clashofclans-bases/main/public/images/logo.png\"
              },
              \"footer\": {
                \"text\": \"GitHub Commit Notifier • ${{ github.repository }}\"
              },
              \"timestamp\": \"$(date -u +'%Y-%m-%dT%H:%M:%SZ')\"
            }]
          }" $DISCORD_WEBHOOK
