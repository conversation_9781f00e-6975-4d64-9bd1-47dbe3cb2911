# Ignore SQL database backups
*.sql
# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
.idea
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# eslint cache
.eslintcache

# next-sitemap
robots.txt
sitemap.xml
sitemap-*.xml
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/

# copilot context
copilot-context.ts

# VSCode settings
.vscode/

# Mac system files
__MACOSX/

# Log files
*.log

# Prisma
/prisma/dev.db
/prisma/dev.db-journal

# Uploads (if not needed in repo)
/public/uploads/

# Security sensitive files
/scripts/print-required-envs.sh
*.key
*.pem
*.cert
*.secret
*secret*
*credential*
*password*
*token*
*api-key*
