<a href="https://clashofclans-bases.vercel.app/">
<!-- New Cover -->
<img src="/public/images/githubCover.png" alt="Clash of Clans Bases Cover">
</a>
<p align="center">
  <a href="#-features"><strong>Features</strong></a> ·
  <a href="#-base-categories"><strong>Base Categories</strong></a> ·
  <a href="#-deployment"><strong>Deployment</strong></a> ·
  <a href="#-getting-started"><strong>Getting Started</strong></a> ·
  <a href="#%EF%B8%8F-scripts-overview"><strong>Scripts</strong></a> ·
  <a href="#-contribution"><strong>Contribution</strong></a>
</p>

# Clash of Clans Base Repository

A comprehensive collection of Clash of Clans base layouts for both Town Hall and Builder Hall, organized by level and category. This repository allows players to browse, filter, and copy base links directly into the game.

## 🎉 Features

- 🏠 **Extensive Base Collection** - War, Farming, Trophy, and Hybrid bases for all TH and BH levels
- � **One-Click Copy** - Copy base links directly to your clipboard for easy import to the game
- 🔍 **Advanced Filtering** - Find bases by Town Hall level, Builder Hall level, and category
- 📊 **View Tracking** - See the popularity of different base designs
- 👑 **Latest Updates** - Regular updates with the newest meta bases

## 🏰 Base Categories

The repository organizes Clash of Clans bases into several categories:

| Category    | Description                                            |
| ----------- | ------------------------------------------------------ |
| **War**     | Designed to defend against 3-star attacks in clan wars |
| **Farming** | Optimized to protect resources from raiders            |
| **Trophy**  | Focused on preventing stars to maintain trophy count   |
| **Hybrid**  | Balanced bases that work well for multiple purposes    |

## 💻 Tech Stack

- 🚀 **Next.js 14** - React framework with server-side rendering
- ⚛️ **React 18** - Component-based UI
- 🔵 **TypeScript** - Type-safe code
- 🎨 **TailwindCSS** - Utility-first CSS
- 🔵 **Prisma** - Type-safe database ORM
- 🔒 **NextAuth** - Authentication solution
- 📋 **React Hook Form** - Form management
- 💾 **PostgreSQL** - Robust database
- ☁️ **Cloudinary** - Image hosting and optimization
- � **Zod** - Schema validation

## 🚀 Deployment

The application is deployed on Vercel. You can visit the live site at:

[https://clashofclans-bases.vercel.app/](https://clashofclans-bases.vercel.app/)

## � Usage

1. **Browse Bases by Type**
   - Navigate to Town Hall or Builder Hall sections
   - Select your desired hall level (TH3-TH17 or BH3-BH10)

2. **Copy Base Links**
   - Click on a base to view details
   - Use the "Copy Link" button to copy the base link to your clipboard
   - Open Clash of Clans and paste the link to import the base

3. **Filtering**
   - Filter bases by category (War, Farming, Trophy, Hybrid)
   - Sort by newest or most popular

## 🎯 Getting Started

### 1. Clone the repository

```bash
git clone https://github.com/farhanf7n/clashofclans-bases.git
cd clashofclans-bases
```

### 2. Install dependencies

```bash
npm install
```

### 3. Set up environment variables

Create a `.env` file with the following variables:

```
DATABASE_URL="postgresql://username:password@localhost:5432/clashofclans"
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-here"
CLOUDINARY_CLOUD_NAME="your-cloud-name"
CLOUDINARY_API_KEY="your-api-key"
CLOUDINARY_API_SECRET="your-api-secret"
ADMIN_USERNAME="admin"
ADMIN_PASSWORD="secure-password"
```

### 4. Set up the database

```bash
npx prisma generate
npx prisma db push
```

### 5. Run the development server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application.

## 📁 Project Structure

```bash
.
├── prisma/                         # Prisma schema and migrations
│   ├── schema.prisma               # Database schema
│   └── migrations/                 # Database migrations
├── public/                         # Static assets
│   ├── images/                     # Base preview images
│   │   ├── TownHalls/              # Town Hall images (th3.png - th17.png)
│   │   └── BuilderHalls/           # Builder Hall images (bh3.png - bh10.png)
│   └── favicon/                    # Favicon files
├── src/
│   ├── actions/                    # Server actions
│   ├── app/                        # Next.js App Router structure
│   │   ├── admin/                  # Admin dashboard
│   │   ├── base/                   # Base detail pages
│   │   ├── town-hall/              # Town Hall pages
│   │   └── builder-hall/           # Builder Hall pages
│   ├── components/                 # React components
│   │   └── CategoryCardClient.tsx  # Base category cards
│   ├── hooks/                      # Custom React hooks
│   ├── lib/                        # Utility functions
│   └── store/                      # Redux store configuration
└── scripts/                        # Utility scripts
```

## ⚙️ Scripts Overview

The following scripts are available in the `package.json`:

- `dev`: Run development server with automatic Prisma generation
- `build`: Generate Prisma client, push DB schema and build the Next.js app
- `start`: Start the production server
- `preview`: Build and start the app in production mode
- `lint` and `lint:fix`: Check and fix code style issues
- `format:check` and `format:write`: Check and fix code formatting
- `typecheck`: Verify TypeScript types without generating output files
- `test` and `test:watch`: Run Jest tests (once or in watch mode)

## 🔧 Admin Features

The admin dashboard allows authorized users to:

- Add new base layouts with images and links
- Delete outdated bases
- View analytics for base popularity
- Filter and sort bases for management

Access the admin interface at `/admin` with the credentials set in your environment variables.

## 🤝 Contribution

To contribute to this project:

1. Fork the repository
2. Create a new branch (`git checkout -b feature/your-feature`)
3. Make your changes
4. Commit your changes (`git commit -m 'Add some feature'`)
5. Push to the branch (`git push origin feature/your-feature`)
6. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## ❓ Support

For questions or issues, please open an issue on the GitHub repository.

---

Made with ❤️ by [farhanf7n](https://github.com/farhanf7n)
