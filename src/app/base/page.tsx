'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

// This page redirects to the default Town Hall 17 page
export default function BasesRedirectPage() {
  const router = useRouter();

  useEffect(() => {
    router.replace('/base/town-hall/17?sort=popular');
  }, [router]);

  return (
    <div className="flex h-[60vh] w-full items-center justify-center">
      <div className="text-center">
        <div className="mx-auto mb-4 size-12 animate-spin rounded-full border-4 border-t-blue-600"></div>
        <h1 className="text-xl font-semibold">
          Loading Clash of Clans Bases...
        </h1>
        <p className="text-sm text-gray-500">
          Redirecting to Town Hall 17 bases
        </p>
      </div>
    </div>
  );
}
