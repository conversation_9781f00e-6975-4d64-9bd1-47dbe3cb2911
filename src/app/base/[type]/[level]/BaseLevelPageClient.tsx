'use client';

import { useEffect, useMemo, useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';

import { CategoryViewManager } from '@/components/analytics/CategoryViewManager';
import { BaseSidebar } from '@/components/bases/BaseSidebar';
import { FilterChips } from '@/components/bases/FilterChips';
import { useFilterContext } from '@/context/FilterContext';
import { usePrefetchRoute } from '@/hooks/usePrefetchRoute';
import { formatDate, formatViewCount } from '@/lib/utils';

// Analytics components removed as per requirement

interface Base {
  id: string;
  type: 'BUILDER_HALL' | 'TOWN_HALL';
  level: number;
  title?: string | null;
  imageUrl: string;
  baseLink?: string | null;
  views: number;
  createdAt: string;
  sortOrder?: number;
  category?: string;
}

// Import type from page or define it locally
type BaseType = 'TOWN_HALL' | 'BUILDER_HALL';

interface BaseLevelPageClientProps {
  type: 'town-hall' | 'builder-hall';
  lvl: number;
  sort: string;
  bases: Base[] | undefined;
  initialVisits: number;
  baseType?: BaseType;
}

const BASES_PER_PAGE = 12;
// Lazy load BaseCard for future optimization (if you modularize it)

export default function BaseLevelPageClient({
  type,
  lvl,
  sort,
  bases,
  initialVisits,
  baseType,
}: BaseLevelPageClientProps) {
  const prefetchRoute = usePrefetchRoute();
  const isLoading = bases === undefined;
  const skeletons = Array.from({ length: BASES_PER_PAGE });

  // Pagination state
  const [page, setPage] = useState(1);

  // Access the filter context to get the actual selected category
  let selectedCategory: string = 'ALL';
  try {
    const filterContext = useFilterContext();
    selectedCategory = filterContext?.category || 'ALL';
  } catch {
    // fallback
  }

  // Memoize paginated bases
  const paginatedBases = useMemo(() => {
    if (!bases) return [];
    const start = (page - 1) * BASES_PER_PAGE;
    return bases.slice(start, start + BASES_PER_PAGE);
  }, [bases, page]);

  const totalPages = useMemo(() => {
    if (!bases) return 1;
    return Math.max(1, Math.ceil(bases.length / BASES_PER_PAGE));
  }, [bases]);

  // Reset to page 1 if bases change (e.g., filter changes)
  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => {
    setPage(1);
  }, [bases]);

  return (
    <>
      <div className="mx-auto h-[414px]">
        <Image
          width={3000}
          height={600}
          src="/images/detailPageCover.jpg"
          alt="War Base Strategy"
          className="size-full object-cover"
        />
      </div>
      <main className="w-full px-4 py-24 sm:px-6 lg:px-8">
        <div className="flex flex-col gap-8 lg:flex-row">
          <div className="w-full lg:w-1/4">
            <BaseSidebar
              initialType={type}
              initialLevel={lvl}
              initialCategory={selectedCategory}
              initialSort={sort}
              onFilterChange={() => {}}
            />
          </div>
          <div className="w-full lg:w-3/4">
            <h1 className="mb-6 text-2xl font-bold">
              {type === 'town-hall'
                ? `Town Hall ${lvl} Bases`
                : `Builder Hall ${lvl} Bases`}{' '}
              <span className="text-sm font-normal text-gray-500">
                (
                <CategoryViewManager
                  type={
                    baseType ||
                    (type === 'town-hall' ? 'TOWN_HALL' : 'BUILDER_HALL')
                  }
                  level={lvl}
                  initialVisits={initialVisits}
                />{' '}
                views)
              </span>
            </h1>
            <FilterChips
              type={type}
              level={lvl}
              category={
                type === 'town-hall' && selectedCategory !== 'ALL'
                  ? selectedCategory
                  : undefined
              }
              sort={sort}
            />

            {/* Pagination controls (top) */}
            <div className="mb-4 flex items-center justify-between">
              <span className="text-sm text-gray-500">
                {' '}
                Showing {isLoading ? 0 : paginatedBases.length} of{' '}
                {bases?.length ?? 0} bases
              </span>
              <div className="flex items-center gap-2">
                <button
                  className="rounded bg-gray-200 px-3 py-1 text-sm font-medium disabled:opacity-50"
                  onClick={() => setPage((p) => Math.max(1, p - 1))}
                  disabled={page === 1}
                >
                  Previous
                </button>
                <span className="text-sm">
                  Page {page} of {totalPages}
                </span>
                <button
                  className="rounded bg-gray-200 px-3 py-1 text-sm font-medium disabled:opacity-50"
                  onClick={() => setPage((p) => Math.min(totalPages, p + 1))}
                  disabled={page === totalPages}
                >
                  Next
                </button>
              </div>
            </div>

            <div className="grid-col-1 grid gap-4 md:grid-cols-3">
              {isLoading
                ? skeletons.map((_, i) => (
                    <div
                      key={i}
                      className="flex animate-pulse items-center gap-4 rounded-lg border bg-gray-100 p-4"
                    >
                      <div className="size-32 rounded bg-gray-300" />
                      <div className="flex-1 space-y-2">
                        <div className="h-6 w-1/2 rounded bg-gray-300" />
                        <div className="h-4 w-1/3 rounded bg-gray-200" />
                        <div className="h-4 w-1/4 rounded bg-gray-200" />
                      </div>
                    </div>
                  ))
                : paginatedBases.length === 0 && (
                    <div className="col-span-3 rounded-lg border border-dashed border-gray-300 bg-gray-50 p-8 text-center">
                      <svg
                        className="mx-auto mb-4 size-12 text-gray-400"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={1.5}
                          d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m6.75 12H9m1.5-12H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"
                        />
                      </svg>
                      <h3 className="mb-1 text-lg font-medium">
                        No bases found
                      </h3>
                      <p className="text-gray-500">
                        No matching bases for{' '}
                        {type === 'town-hall' ? 'Town Hall' : 'Builder Hall'}{' '}
                        {lvl} with the selected filters.
                      </p>
                    </div>
                  )}
              {!isLoading &&
                paginatedBases.map((base) => (
                  <Link
                    key={base.id}
                    href={`/base/${type}/${lvl}/base/${base.id}`}
                    className="bg-secondary flex flex-col items-center gap-3 rounded-lg border p-3 transition hover:shadow-sm"
                    role="button"
                    tabIndex={0}
                    style={{ cursor: 'pointer' }}
                    prefetch={true}
                    onMouseEnter={() =>
                      prefetchRoute(`/base/${type}/${lvl}/base/${base.id}`)
                    }
                  >
                    <div className="relative block aspect-[4/3] overflow-hidden rounded-lg">
                      {/* Put views date etc here */}
                      {/* Added Date */}
                      <div className="absolute right-2 top-2 flex w-fit items-center gap-2 rounded-md border bg-[#f5f6f8] px-2 py-1 font-mono text-xs font-extrabold text-gray-600">
                        <svg
                          className="size-4"
                          width="16"
                          height="16"
                          viewBox="0 0 16 16"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M10.6641 9.33203V10.7987L11.7307 11.4654"
                            stroke="black"
                            strokeWidth="1.3"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                          <path
                            d="M10.6641 1.33203V3.9987"
                            stroke="black"
                            strokeWidth="1.3"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                          <path
                            d="M14 5.0013V4.0013C14 3.64768 13.8595 3.30854 13.6095 3.05849C13.3594 2.80844 13.0203 2.66797 12.6667 2.66797H3.33333C2.97971 2.66797 2.64057 2.80844 2.39052 3.05849C2.14048 3.30854 2 3.64768 2 4.0013V13.3346C2 13.6883 2.14048 14.0274 2.39052 14.2774C2.64057 14.5275 2.97971 14.668 3.33333 14.668H5.66667"
                            stroke="black"
                            strokeWidth="1.3"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                          <path
                            d="M2 6.66797H5.33333"
                            stroke="black"
                            strokeWidth="1.3"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                          <path
                            d="M5.33594 1.33203V3.9987"
                            stroke="black"
                            strokeWidth="1.3"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                          <path
                            d="M10.6641 14.668C12.8732 14.668 14.6641 12.8771 14.6641 10.668C14.6641 8.45883 12.8732 6.66797 10.6641 6.66797C8.45492 6.66797 6.66406 8.45883 6.66406 10.668C6.66406 12.8771 8.45492 14.668 10.6641 14.668Z"
                            stroke="black"
                            strokeWidth="1.3"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>

                        {formatDate(base.createdAt)}
                      </div>
                      <Image
                        src={
                          typeof base.imageUrl === 'string' && base.imageUrl
                            ? base.imageUrl
                            : '/images/placeholder.png'
                        }
                        alt={base.title ?? 'Base image'}
                        width={693}
                        height={541}
                        className="size-full rounded-lg object-cover"
                        loading="lazy"
                        unoptimized
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = '/images/placeholder.png';
                        }}
                      />

                      <div className="absolute bottom-2 right-2 flex items-center gap-2">
                        {/* Total time Copy & Open Base Clicked */}
                        <div className="flex w-fit items-center gap-2 rounded-md border bg-[#f5f6f8] px-2 py-1 font-mono text-xs font-extrabold text-gray-600">
                          <svg
                            className="size-4"
                            width="16"
                            height="16"
                            viewBox="0 0 16 16"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M8 10V2"
                              stroke="black"
                              strokeWidth="1.3"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                            <path
                              d="M14 10V12.6667C14 13.0203 13.8595 13.3594 13.6095 13.6095C13.3594 13.8595 13.0203 14 12.6667 14H3.33333C2.97971 14 2.64057 13.8595 2.39052 13.6095C2.14048 13.3594 2 13.0203 2 12.6667V10"
                              stroke="black"
                              strokeWidth="1.3"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                            <path
                              d="M4.66406 6.66797L7.9974 10.0013L11.3307 6.66797"
                              stroke="black"
                              strokeWidth="1.3"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>{' '}
                          {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
                          {(base as any).downloadCount ?? 0}
                        </div>

                        {/* Total Views */}
                        <div className="flex w-fit items-center gap-2 rounded-md border bg-[#f5f6f8] px-2 py-1 font-mono text-xs font-extrabold text-gray-600">
                          <svg
                            className="size-4"
                            width="16"
                            height="16"
                            viewBox="0 0 16 16"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M1.37761 8.23029C1.32205 8.08061 1.32205 7.91596 1.37761 7.76629C1.91874 6.45419 2.83728 5.33231 4.01679 4.54289C5.19629 3.75346 6.58364 3.33203 8.00294 3.33203C9.42225 3.33203 10.8096 3.75346 11.9891 4.54289C13.1686 5.33231 14.0871 6.45419 14.6283 7.76629C14.6838 7.91596 14.6838 8.08061 14.6283 8.23029C14.0871 9.54238 13.1686 10.6643 11.9891 11.4537C10.8096 12.2431 9.42225 12.6645 8.00294 12.6645C6.58364 12.6645 5.19629 12.2431 4.01679 11.4537C2.83728 10.6643 1.91874 9.54238 1.37761 8.23029Z"
                              stroke="black"
                              strokeWidth="1.3"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                            <path
                              d="M8 10C9.10457 10 10 9.10457 10 8C10 6.89543 9.10457 6 8 6C6.89543 6 6 6.89543 6 8C6 9.10457 6.89543 10 8 10Z"
                              stroke="black"
                              strokeWidth="1.3"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                          {formatViewCount(base.views)}
                        </div>
                      </div>
                    </div>
                    <div className="flex  w-full flex-wrap items-start justify-between gap-2">
                      <div className="flex w-fit items-center gap-2 rounded-md border bg-[#f5f6f8] px-2 py-1 font-mono text-sm font-extrabold uppercase text-gray-600">
                        {/* Town Hall / Builder Hall Image with Size-4*/}
                        {base.type === 'BUILDER_HALL' ? (
                          <Image
                            src={`/images/BuilderHalls/bh${base.level}.png`}
                            alt={`Builder Hall ${base.level}`}
                            width={16}
                            height={16}
                            className="size-4 object-contain"
                          />
                        ) : (
                          <Image
                            src={`/images/TownHalls/th${base.level}.png`}
                            alt={`Town Hall ${base.level}`}
                            width={16}
                            height={16}
                            className="size-4 object-contain"
                          />
                        )}
                        {base.type === 'BUILDER_HALL'
                          ? 'Builder Hall'
                          : 'Town Hall'}
                        <span>{base.level}</span>
                        {base.type === 'TOWN_HALL' && (
                          <span> {base.category} Base</span>
                        )}
                        {base.type === 'BUILDER_HALL' && <span> Base</span>}
                      </div>
                      {/* Type of Base */}
                      <div className="flex w-fit items-center gap-2 rounded-md border bg-[#fff07c33] px-2 py-1 text-black">
                        <svg
                          className="size-4 dark:invert"
                          width="21"
                          height="18"
                          viewBox="0 0 21 18"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M2 9L6 2.5L10 9H2Z"
                            stroke="black"
                            strokeWidth="1.2"
                          ></path>
                          <path
                            d="M15 2L11 9H19L15 2Z"
                            stroke="black"
                            strokeWidth="1.2"
                          ></path>
                          <path
                            d="M6.5 17L10.5 10L14.5 17H6.5Z"
                            stroke="black"
                            strokeWidth="1.2"
                          ></path>
                        </svg>

                        <span className="font-mono text-xs font-extrabold uppercase dark:text-white">
                          {base.category} Base
                        </span>
                      </div>
                    </div>
                  </Link>
                ))}
            </div>

            {/* Pagination controls (bottom) */}
            {totalPages > 1 && (
              <div className="mt-6 flex items-center justify-center gap-2">
                <button
                  className="rounded bg-gray-200 px-3 py-1 text-sm font-medium disabled:opacity-50"
                  onClick={() => setPage((p) => Math.max(1, p - 1))}
                  disabled={page === 1}
                >
                  Previous
                </button>
                <span className="text-sm">
                  Page {page} of {totalPages}
                </span>
                <button
                  className="rounded bg-gray-200 px-3 py-1 text-sm font-medium disabled:opacity-50"
                  onClick={() => setPage((p) => Math.min(totalPages, p + 1))}
                  disabled={page === totalPages}
                >
                  Next
                </button>
              </div>
            )}
          </div>
        </div>
      </main>
    </>
  );
}
