import Image from 'next/image';
import { notFound } from 'next/navigation';

import CopyBaseButton from './CopyBaseButton';

import { BaseViewManager } from '@/components/analytics/BaseViewManager';
import { BackButton } from '@/components/ui/back-button';
import prisma from '@/lib/prisma';

export const dynamic = 'force-dynamic';

export default async function BaseDetailPage({
  params,
}: {
  params: { type: string; level: string; baseId: string };
}) {
  const { type, level, baseId } = params;
  const lvl = parseInt(level, 10);
  if (!baseId || isNaN(lvl)) return notFound();

  let dbType: 'TOWN_HALL' | 'BUILDER_HALL' | undefined;
  if (type === 'town-hall') dbType = 'TOWN_HALL';
  else if (type === 'builder-hall') dbType = 'BUILDER_HALL';
  else return notFound();

  const base = await prisma.base.findUnique({
    where: { id: baseId, level: lvl, type: dbType },
  });

  if (!base) return notFound();

  return (
    <>
      <div className="mx-auto h-[414px]">
        <Image
          width={3000}
          height={600}
          src="/images/detailPageCover.jpg"
          alt="War Base Strategy"
          className="size-full object-cover"
        />
      </div>
      <main className="mx-auto max-w-5xl gap-8 py-24">
        {/* Hydrate and BaseViewTracker removed - consolidated with BaseViewManager */}

        <div className="mb-4 flex flex-wrap items-center justify-between gap-4">
          <BackButton />

          <h2 className="font-supercell flex items-center gap-2 text-2xl font-bold tracking-tight">
            <span className="block">
              {base.type === 'BUILDER_HALL' ? 'Builder Hall' : 'Town Hall'}
            </span>
            <span>{base.level}</span>
            {base.type === 'TOWN_HALL' && <span> {base.category} Base</span>}
            {base.type === 'BUILDER_HALL' && <span> Base</span>}
          </h2>

          {/* Views */}
          <p className="focus:ring-ring bg-secondary text-secondary-foreground  inline-flex items-center rounded-md border border-transparent px-4 py-2 text-sm font-normal transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2">
            <span className="flex items-start space-x-2 text-sm">
              <svg
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="icon-eye"
              >
                <g clipPath="url(#clip0_4749_461)">
                  <mask
                    id="mask0_4749_461"
                    style={{ maskType: 'luminance' }}
                    maskUnits="userSpaceOnUse"
                    x="0"
                    y="0"
                    width="20"
                    height="20"
                  >
                    <path d="M20 0H0V20H20V0Z" fill="white" />
                  </mask>
                  <g mask="url(#mask0_4749_461)">
                    <mask
                      id="mask1_4749_461"
                      style={{ maskType: 'luminance' }}
                      maskUnits="userSpaceOnUse"
                      x="0"
                      y="0"
                      width="20"
                      height="20"
                    >
                      <path d="M20 0H0V20H20V0Z" fill="white" />
                    </mask>
                    <g mask="url(#mask1_4749_461)">
                      <path
                        d="M9.99845 3.33203C13.5734 3.33203 16.5509 5.3562 18.8859 9.2937L19.0692 9.60953L19.1067 9.69287L19.1317 9.76203L19.1434 9.80787L19.155 9.8762L19.1642 9.95953V10.0512L19.1525 10.1437C19.1471 10.1747 19.1398 10.2053 19.1309 10.2354L19.0984 10.3254L19.0684 10.3879L19.055 10.4129C16.7517 14.4429 13.805 16.5629 10.2592 16.6621L9.99845 16.6654C6.3351 16.6654 3.30093 14.5412 0.941763 10.4121C0.869854 10.2862 0.832031 10.1437 0.832031 9.9987C0.832031 9.85372 0.869854 9.71125 0.941763 9.58537C3.30093 5.4562 6.3351 3.33203 9.99845 3.33203ZM9.99845 7.4987C9.33539 7.4987 8.69951 7.76209 8.23066 8.23093C7.76182 8.69977 7.49843 9.33566 7.49843 9.9987C7.49843 10.6618 7.76182 11.2976 8.23066 11.7665C8.69951 12.2353 9.33539 12.4987 9.99845 12.4987C10.6614 12.4987 11.2973 12.2353 11.7661 11.7665C12.235 11.2976 12.4984 10.6618 12.4984 9.9987C12.4984 9.33566 12.235 8.69977 11.7661 8.23093C11.2973 7.76209 10.6614 7.4987 9.99845 7.4987Z"
                        fill="currentColor"
                      />
                    </g>
                  </g>
                </g>
                <defs>
                  <clipPath id="clip0_4749_461">
                    <rect width="20" height="20" fill="white" />
                  </clipPath>
                </defs>
              </svg>

              <span className="text-primary pr-1 font-bold">
                <BaseViewManager baseId={base.id} initialViews={base.views} />
                {' Views'}
              </span>
            </span>
          </p>
        </div>

        <div className="relative w-full">
          <Image
            src={
              typeof base.imageUrl === 'string' && base.imageUrl?.length > 0
                ? base.imageUrl
                : '/images/placeholder.png'
            }
            alt={`Base ${base.id}`}
            width={1792}
            height={828}
            className="mt-4 h-auto w-full rounded-lg bg-gray-100 object-contain"
            priority
            unoptimized
          />
          <div className="absolute right-4 top-4 flex items-center gap-2 rounded-lg bg-yellow-300 px-4 py-2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="1.75"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="lucide lucide-calendar-clock-icon lucide-calendar-clock text-black"
            >
              <path d="M16 14v2.2l1.6 1" />
              <path d="M16 2v4" />
              <path d="M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5" />
              <path d="M3 10h5" />
              <path d="M8 2v4" />
              <circle cx="16" cy="16" r="6" />
            </svg>
            <span className="text-sm font-semibold text-black">
              {base.createdAt.toISOString().split('T')[0]}
            </span>
          </div>
        </div>

        <div className="my-6 flex items-center justify-between gap-4">
          {/* Total time Copy & Open Base Clicked */}
          <div className="focus:ring-ring bg-secondary text-secondary-foreground hover:bg-secondary/80 flex items-center gap-2 rounded-md border border-transparent px-4 py-2 text-sm font-normal transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2">
            <svg
              className="size-6"
              width="18"
              height="18"
              viewBox="0 0 18 18"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              {' '}
              <path
                d="M7.44688 16H11.6469C15.1469 16 16.5469 14.6 16.5469 11.1V6.90001C16.5469 3.4 15.1469 2 11.6469 2H7.44688C3.94688 2 2.54688 3.4 2.54688 6.90001V11.1C2.54688 14.6 3.94688 16 7.44688 16Z"
                fill="black"
              />{' '}
              <path
                d="M7.44531 8.65625L9.54531 10.7563L11.6453 8.65625"
                stroke="#EEEEEE"
                strokeWidth="0.72"
                strokeLinecap="round"
                strokeLinejoin="round"
              />{' '}
              <path
                d="M9.54688 10.7563V5.15625"
                stroke="#EEEEEE"
                strokeWidth="0.72"
                strokeLinecap="round"
                strokeLinejoin="round"
              />{' '}
            </svg>
            Downloads:{' '}
            {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
            {(base as any).downloadCount ?? 0}
          </div>
          {/* Copy Base Button */}
          <CopyBaseButton baseId={base.id} baseLink={base.baseLink} />
        </div>

        {/* Divider */}
        <div className="relative mt-8 flex items-center justify-center">
          <div className="text-muted-foreground relative h-px w-full">
            <div className="h-px w-full bg-[repeating-linear-gradient(90deg,transparent,transparent_4px,currentColor_4px,currentColor_10px)] [mask-image:linear-gradient(90deg,transparent,black_25%,black_75%,transparent)]"></div>
          </div>
        </div>

        {/* Share Base on Socials */}
        <div className="pt-8">
          <div className="mb-4 text-center">
            <h3 className="font-supercell text-xl">
              Please Share the Base Layout with your Friends
            </h3>
          </div>

          <div className="flex flex-wrap items-center justify-center gap-4">
            {/* Facebook */}
            <a
              href={`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(base.baseLink ?? '')}`}
              target="_blank"
              rel="noopener noreferrer"
              aria-label="Share on Facebook"
              className="rounded-full bg-[#3b5998] p-2 transition-transform hover:scale-110"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="white"
              >
                <path d="M18 2h-3a5 5 0 00-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 011-1h3z" />
              </svg>
            </a>

            {/* Twitter */}
            <a
              href={`https://twitter.com/intent/tweet?text=${encodeURIComponent(`Check out this awesome Clash of Clans base layout! ${base.baseLink ?? ''}`)}`}
              target="_blank"
              rel="noopener noreferrer"
              aria-label="Share on Twitter"
              className="rounded-full bg-[#1DA1F2] p-2 transition-transform hover:scale-110"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="white"
              >
                <path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z" />
              </svg>
            </a>

            {/* WhatsApp */}
            <a
              href={`https://api.whatsapp.com/send?text=${encodeURIComponent(`Check out this awesome Clash of Clans base layout! ${base.baseLink ?? ''}`)}`}
              target="_blank"
              rel="noopener noreferrer"
              aria-label="Share on WhatsApp"
              className="rounded-full bg-[#25D366] p-2 transition-transform hover:scale-110"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="white"
              >
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413Z" />
              </svg>
            </a>

            {/* Telegram */}
            <a
              href={`https://telegram.me/share/url?url=${encodeURIComponent(base.baseLink ?? '')}&text=${encodeURIComponent(`Check out this awesome Clash of Clans base layout!`)}`}
              target="_blank"
              rel="noopener noreferrer"
              aria-label="Share on Telegram"
              className="rounded-full bg-[#0088cc] p-2 transition-transform hover:scale-110"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="white"
              >
                <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.96 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z" />
              </svg>
            </a>
          </div>
        </div>
      </main>
    </>
  );
}
