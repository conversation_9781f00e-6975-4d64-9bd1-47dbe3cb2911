'use client';

import { useState } from 'react';
import { toast } from 'sonner';

import { incrementBaseDownloadCount } from '@/actions/base-download';
import { Button } from '@/components/ui/button';

interface CopyBaseButtonProps {
  baseLink: string | null;
  baseId: string;
}

export default function CopyBaseButton({
  baseLink,
  baseId,
}: CopyBaseButtonProps) {
  const [isClicked, setIsClicked] = useState(false);

  // Directly use the server action to track downloads

  const handleCopyAndRedirect = async () => {
    if (!baseLink) return;

    try {
      // Visual feedback for click
      setIsClicked(true);
      setTimeout(() => setIsClicked(false), 1000);

      // Track the download action
      try {
        await incrementBaseDownloadCount(baseId);
      } catch (error) {
        console.error('Failed to track download:', error);
        // Continue with copy and redirect even if tracking fails
      }

      // Copy the link to clipboard
      navigator.clipboard.writeText(baseLink);

      // Show toast notification
      toast.success('Link copied! Opening base...', {
        duration: 1500,
      });

      // Use setTimeout to ensure the UI updates before navigation
      setTimeout(() => {
        try {
          // Try direct window.open first
          const newWindow = window.open(
            baseLink,
            '_blank',
            'noopener,noreferrer'
          );

          // If that fails (popup blocker), use an anchor element click
          if (!newWindow) {
            console.log('Window open failed, trying anchor method');
            const anchor = document.createElement('a');
            anchor.href = baseLink;
            anchor.target = '_blank';
            anchor.rel = 'noopener noreferrer';
            anchor.style.display = 'none';
            document.body.appendChild(anchor);
            anchor.click();
            document.body.removeChild(anchor);

            // Inform the user if we had to use the fallback method
            toast.info('Please allow popups to open the base directly', {
              duration: 3000,
            });
          }
        } catch (e) {
          console.error('Navigation error:', e);
          // Final fallback - just alert the user
          toast.info(
            'Could not open automatically. The link is copied to your clipboard.',
            {
              duration: 3000,
            }
          );
        }
      }, 100);
    } catch (error) {
      console.error('Error during copy/open:', error);
      toast.error('Could not open link. Please try again.');
    }
  };

  return (
    <Button
      variant="default"
      className={`transition-all ${isClicked ? 'scale-95 bg-green-600' : 'hover:scale-105'} flex items-center gap-2`}
      onClick={handleCopyAndRedirect}
    >
      {isClicked ? (
        <>
          <span>Opening Base...</span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="animate-spin"
          >
            <path d="M21 12a9 9 0 1 1-6.219-8.56" />
          </svg>
        </>
      ) : (
        <>
          <span>Copy & Open Base</span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6" />
            <polyline points="15 3 21 3 21 9" />
            <line x1="10" y1="14" x2="21" y2="3" />
          </svg>
        </>
      )}
    </Button>
  );
}
