// Use a string union for BaseType due to Prisma/TS bug
export type BaseType = 'TOWN_HALL' | 'BUILDER_HALL';

import { notFound } from 'next/navigation';

import BaseLevelPageClient from './BaseLevelPageClient';

import prisma from '@/lib/prisma';

const TYPE_MAP: Record<string, BaseType> = {
  'town-hall': 'TOWN_HALL',
  'builder-hall': 'BUILDER_HALL',
};

export const dynamic = 'force-dynamic';

export default async function BaseLevelPage({
  params,
  searchParams,
}: {
  params: { type: string; level: string };
  searchParams: {
    type?: string | null | undefined;
    sort?: string | null | undefined;
    category?: string | null | undefined;
  };
}) {
  const { type, level } = params;
  const baseType = TYPE_MAP[type];
  const lvl = parseInt(level, 10);

  // Validation
  if (!baseType) {
    return notFound();
  }
  if (baseType === 'TOWN_HALL' && (isNaN(lvl) || lvl < 3 || lvl > 17)) {
    return notFound();
  }
  if (baseType === 'BUILDER_HALL' && (isNaN(lvl) || lvl < 1 || lvl > 10)) {
    return notFound();
  }

  const sort = searchParams.sort || 'newest';
  const category = searchParams.category as string | undefined;

  // Build the order by clause based on sort parameter
  const orderBy =
    sort === 'oldest'
      ? { createdAt: 'asc' as const }
      : sort === 'popular'
        ? { views: 'desc' as const }
        : { createdAt: 'desc' as const };

  // Build the where clause for filtering
  const whereClause = {
    type: baseType,
    level: lvl,
  } as Record<string, string | number | boolean>;

  // Add category filter for Town Hall bases only
  if (baseType === 'TOWN_HALL' && category) {
    whereClause.category = category;
  }

  // Fetch bases with filters
  const bases = await prisma.base.findMany({
    where: whereClause,
    orderBy,
  });

  // Fetch the current category visit count (optional, only for Town Hall)
  let initialVisits = 0;
  if (baseType === 'TOWN_HALL') {
    const categoryVisit = await prisma.categoryVisit.findUnique({
      where: { type_level: { type: 'TOWN_HALL', level: lvl } },
    });
    initialVisits = categoryVisit?.visits ?? 0;
  }

  // Convert Date objects to strings for the client component
  const formattedBases = bases.map((base) => ({
    ...base,
    createdAt: base.createdAt.toISOString(),
  }));

  return (
    <BaseLevelPageClient
      bases={formattedBases}
      initialVisits={initialVisits}
      lvl={lvl}
      type={type as 'town-hall' | 'builder-hall'}
      sort={sort}
      baseType={baseType}
    />
  );
}
