import '@/styles/globals.css';

import { PropsWithChildren } from 'react';
import { LanguageProvider } from '@inlang/paraglide-next';
import { Analytics } from '@vercel/analytics/next';
import type { Metadata, Viewport } from 'next';
import { Toaster as SonnerToaster } from 'sonner';

import AuthSessionProvider from '@/components/auth-session-provider';
import { Footer } from '@/components/footer';
import LoadingIndicator from '@/components/LoadingIndicator';
import { Navbar as NavbarClient } from '@/components/navbar/navbar-client';
import { SuppressHydrationWarnings } from '@/components/suppress-hydration-warnings';
import { ThemeProvider } from '@/components/theme-provider';
import { ThemeSwitcher } from '@/components/theme-switcher';
import { ErrorBoundary } from '@/components/ui/ErrorBoundary';
import { Toaster } from '@/components/ui/toaster';
import { siteConfig } from '@/lib/constant';
import { fonts } from '@/lib/fonts';
import { cn } from '@/lib/utils';
import { languageTag } from '@/paraglide/runtime.js';

export const viewport: Viewport = {
  themeColor: '#0072b1',
};

export const generateMetadata = (): Metadata => ({
  metadataBase: new URL(siteConfig.url()),
  title: {
    default: siteConfig.title(),
    template: `%s | ${siteConfig.title()}`,
  },
  description: siteConfig.description(),
  keywords: siteConfig.keywords(),
  robots: { index: true, follow: true },
  manifest: '/favicon/site.webmanifest',
  icons: {
    icon: [
      { url: '/favicon/favicon.ico', sizes: '48x48' },
      { url: '/favicon/favicon-16x16.png', sizes: '16x16' },
      { url: '/favicon/favicon-32x32.png', sizes: '32x32' },
    ],
    shortcut: '/favicon/favicon-16x16.png',
    apple: '/favicon/apple-touch-icon.png',
  },

  openGraph: {
    url: siteConfig.url(),
    title: siteConfig.title(),
    description: siteConfig.description(),
    siteName: siteConfig.title(),
    images: [
      {
        url: `${siteConfig.url()}/opengraph-image.png`,
        secureUrl: `${siteConfig.url()}/opengraph-image.png`,
        width: 1200,
        height: 630,
        alt: 'Clash of Clans Bases',
        type: 'image/png',
      },
    ],
    type: 'website',
    locale: 'en_US',
  },
  // Adding additional meta tags specifically for WhatsApp and other platforms
  other: {
    // Standard OpenGraph
    'og:image': `${siteConfig.url()}/opengraph-image.png`,
    'og:image:secure_url': `${siteConfig.url()}/opengraph-image.png`,
    'og:image:width': '1200',
    'og:image:height': '630',
    'og:image:alt': 'Clash of Clans Bases',
    'og:image:type': 'image/png',

    // Alternative format for some crawlers
    'og:image:url': `${siteConfig.url()}/opengraph-image.png`,

    // Facebook and WhatsApp specific
    'fb:app_id': '123456789', // Replace with your actual Facebook app ID if available
  },
  twitter: {
    card: 'summary_large_image',
    title: siteConfig.title(),
    description: siteConfig.description(),
    images: [
      {
        url: `${siteConfig.url()}/opengraph-image.png`,
        width: 1200,
        height: 630,
        alt: 'Clash of Clans Bases',
      },
    ],
    creator: '@cocbases',
  },
  alternates: {
    canonical: siteConfig.url(),
  },
});

const RootLayout = ({ children }: PropsWithChildren) => {
  return (
    <LanguageProvider>
      <html lang={languageTag()} suppressHydrationWarning>
        <body className={cn('relative min-h-screen', fonts)}>
          <SuppressHydrationWarnings />
          <AuthSessionProvider>
            <ThemeProvider attribute="class">
              <NavbarClient />
              <LoadingIndicator />
              <ErrorBoundary>{children}</ErrorBoundary>
              <div className="container mx-auto">
                <div className="relative mt-6 flex items-center justify-center">
                  <div className="text-muted-foreground relative h-px w-full">
                    <div className="h-px w-full bg-[repeating-linear-gradient(90deg,transparent,transparent_4px,currentColor_4px,currentColor_10px)] [mask-image:linear-gradient(90deg,transparent,black_25%,black_75%,transparent)]"></div>
                  </div>
                  <h3 className="bg-muted text-muted-foreground absolute px-3 text-sm font-medium tracking-wide max-md:hidden">
                    FOOTER
                  </h3>
                </div>
              </div>
              <ThemeSwitcher className="absolute bottom-5 right-5 z-10" />

              <Footer />
              <Toaster />
              <SonnerToaster
                position="top-center"
                closeButton
                theme="light"
                richColors
              />
            </ThemeProvider>
          </AuthSessionProvider>
          <Analytics />
        </body>
      </html>
    </LanguageProvider>
  );
};

export default RootLayout;
