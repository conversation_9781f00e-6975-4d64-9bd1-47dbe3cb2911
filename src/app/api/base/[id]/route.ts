import { NextRequest, NextResponse } from 'next/server';

import prisma from '@/lib/prisma';

export async function DELETE(req: NextRequest) {
  const id = req.nextUrl.pathname.split('/').pop();
  if (!id) return NextResponse.json({ error: 'Missing id' }, { status: 400 });
  try {
    await prisma.base.delete({ where: { id } });
    return NextResponse.json({ success: true });
  } catch {
    return NextResponse.json(
      { error: 'Base not found or could not be deleted' },
      { status: 404 }
    );
  }
}
