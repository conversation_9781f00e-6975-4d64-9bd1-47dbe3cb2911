import { NextRequest, NextResponse } from 'next/server';

import { incrementCategoryVisit } from '@/lib/db/category';
import { logError } from '@/lib/logger';
import { categoryVisitSchema } from '@/lib/validate';

// Use a string union for BaseType due to Prisma/TS bug
export type BaseType = 'TOWN_HALL' | 'BUILDER_HALL';

export async function POST(req: NextRequest) {
  if (req.method !== 'POST') {
    return NextResponse.json(
      { success: false, error: 'Method Not Allowed' },
      { status: 405 }
    );
  }
  try {
    const body = await req.json();
    const parse = categoryVisitSchema.safeParse(body);
    if (!parse.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request',
          details: parse.error.errors,
        },
        { status: 422 }
      );
    }
    const { type, level } = parse.data;
    const visit = await incrementCategoryVisit(type as BaseType, level);
    return NextResponse.json(
      { success: true, data: { visits: visit.visits } },
      { status: 200 }
    );
  } catch (e) {
    logError('Error in /api/visit/category', e);
    return NextResponse.json(
      { success: false, error: 'Server error' },
      { status: 500 }
    );
  }
}
