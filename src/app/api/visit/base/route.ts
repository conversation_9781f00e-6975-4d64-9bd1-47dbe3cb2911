import { NextRequest, NextResponse } from 'next/server';

import { incrementBaseViews } from '@/lib/db/base';
import { logError } from '@/lib/logger';
import { baseVisitSchema } from '@/lib/validate';

export async function POST(req: NextRequest) {
  if (req.method !== 'POST') {
    return NextResponse.json(
      { success: false, error: 'Method Not Allowed' },
      { status: 405 }
    );
  }
  try {
    const body = await req.json();
    const parse = baseVisitSchema.safeParse(body);
    if (!parse.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request',
          details: parse.error.errors,
        },
        { status: 422 }
      );
    }
    const { id } = parse.data;
    const base = await incrementBaseViews(id);
    return NextResponse.json(
      { success: true, data: { views: base.views } },
      { status: 200 }
    );
  } catch (e) {
    logError('Error in /api/visit/base', e);
    return NextResponse.json(
      { success: false, error: 'Server error' },
      { status: 500 }
    );
  }
}
