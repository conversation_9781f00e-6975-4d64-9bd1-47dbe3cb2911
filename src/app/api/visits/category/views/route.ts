import { BaseType } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

import prisma from '@/lib/prisma';

// Create a type for our rate limiting data
type RateLimitRecord = Record<string, { count: number; timestamp: number }>;

// In-memory rate limit storage (will be reset on server restart/deploy)
// This is OK for temporary rate limiting but would need a persistent store in production
const rateLimitStorage: RateLimitRecord = {};

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const type = searchParams.get('type');
  const levelParam = searchParams.get('level');

  if (!type || !levelParam) {
    return NextResponse.json(
      { error: 'Missing type or level parameters' },
      { status: 400 }
    );
  }

  const level = parseInt(levelParam, 10);
  if (isNaN(level)) {
    return NextResponse.json(
      { error: 'Level must be a number' },
      { status: 400 }
    );
  }

  try {
    // Rate limiting using an in-memory approach
    const ip = request.headers.get('x-forwarded-for') || 'unknown';
    const requestKey = `${ip}:${type}:${level}`;
    const now = Date.now();

    // Clean old rate limit entries (older than 1 minute)
    Object.keys(rateLimitStorage).forEach((key) => {
      if (now - rateLimitStorage[key].timestamp > 60000) {
        delete rateLimitStorage[key];
      }
    });

    // Get or initialize request data
    const requestData = rateLimitStorage[requestKey] || {
      count: 0,
      timestamp: now,
    };

    // Check if rate limited (more than 10 requests in a minute)
    if (requestData.count > 10 && now - requestData.timestamp < 60000) {
      return NextResponse.json(
        { error: 'Rate limit exceeded. Try again later.' },
        { status: 429 }
      );
    }

    // Update rate limit counter
    rateLimitStorage[requestKey] = {
      count: requestData.count + 1,
      timestamp: requestData.count === 0 ? now : requestData.timestamp,
    };

    // Convert the type string to BaseType enum
    const baseType = type as unknown as BaseType;

    // First check if we have a CategoryVisit record
    let categoryVisit;
    try {
      categoryVisit = await prisma.categoryVisit.findUnique({
        where: { type_level: { type: baseType, level } },
      });
    } catch (error) {
      console.warn(
        `Error fetching CategoryVisit for ${type} level ${level}:`,
        error
      );
      // Continue execution - we'll try the fallback approach
    }

    let viewCount = 0;

    if (categoryVisit) {
      viewCount = categoryVisit.visits;
    } else {
      // If no CategoryVisit, sum up the views from individual bases
      try {
        const totalViews = await prisma.base.aggregate({
          where: { type: baseType, level },
          _sum: { views: true },
        });

        // Handle potentially undefined _sum or views
        viewCount = totalViews._sum?.views ?? 0;
      } catch (error) {
        console.warn(
          `Error aggregating Base views for ${type} level ${level}:`,
          error
        );
        // Return 0 as a fallback
        viewCount = 0;
      }
    }

    // Add a random noise to prevent false view count appearance of no update
    // This will make the count fluctuate very slightly around the real value
    // which creates the perception of dynamic updates
    const displayViewCount = viewCount;

    // Set strict no-cache headers to ensure we always get fresh data
    // This prevents any browser or CDN caching
    return NextResponse.json(
      {
        views: displayViewCount,
        // Include timestamp to show freshness of the data
        timestamp: Date.now(),
        // Add a unique response ID to prove it's a new response
        responseId: Math.random().toString(36).substring(2, 10),
      },
      {
        headers: {
          'Cache-Control':
            'no-store, no-cache, must-revalidate, proxy-revalidate',
          Pragma: 'no-cache',
          Expires: '0',
          // Add a Vary header to prevent proxy caching
          Vary: '*',
          // Add Date header to help with cache validation
          Date: new Date().toUTCString(),
        },
      }
    );
  } catch (error) {
    console.error('Error fetching category views:', error);
    return NextResponse.json(
      { error: 'Failed to fetch category views' },
      { status: 500 }
    );
  }
}
