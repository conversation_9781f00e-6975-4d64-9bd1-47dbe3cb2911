import { BaseType } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

import prisma from '@/lib/prisma';

export async function POST(req: NextRequest) {
  try {
    // Allow query parameters for complete cache bypassing
    const searchParams = req.nextUrl.searchParams;
    const typeFromQuery = searchParams.get('type');
    const levelFromQuery = searchParams.get('level')
      ? parseInt(searchParams.get('level') as string, 10)
      : null;

    // Parse body data (if available)
    let bodyData: Record<string, unknown> = {};
    try {
      bodyData = await req.json();
    } catch (e) {
      // Body might be empty, which is fine when using query params
      console.warn('Failed to parse JSON body:', e);
    }

    // Try to get type/level from query params first, then body
    const typeString = typeFromQuery || (bodyData.type as string);
    const level =
      levelFromQuery !== null ? levelFromQuery : (bodyData.level as number);
    // We're not using force but keep it for future reference
    // const force = bodyData.force === true;

    // Validate type value
    if (!typeString || !['TOWN_HALL', 'BUILDER_HALL'].includes(typeString)) {
      return NextResponse.json(
        { error: 'Invalid type. Must be TOWN_HALL or BUILDER_HALL' },
        { status: 400 }
      );
    }

    // Convert string to BaseType enum
    const type = typeString as BaseType;

    if (typeof level !== 'number') {
      return NextResponse.json(
        { error: 'Missing or invalid level' },
        { status: 400 }
      );
    }

    // Add strict no-cache headers to prevent any caching at all levels
    const headers = new Headers();
    headers.set(
      'Cache-Control',
      'no-store, no-cache, must-revalidate, proxy-revalidate'
    );
    headers.set('Pragma', 'no-cache');
    headers.set('Expires', '0');

    // Generate a unique visit ID to improve tracking and prevent issues with simultaneous requests
    const visitId = `${type}_${level}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

    // Add a retry mechanism for database operations
    const maxRetries = 3;
    let category;
    let retryCount = 0;

    while (retryCount < maxRetries) {
      try {
        // Upsert: create if not exists, else increment
        category = await prisma.categoryVisit.upsert({
          where: { type_level: { type, level } },
          update: { visits: { increment: 1 } },
          create: { type, level, visits: 1 },
        });

        // Success - break out of retry loop
        break;
      } catch (error) {
        retryCount++;

        // If we've exhausted retries, throw the error
        if (retryCount >= maxRetries) {
          console.error(
            `Failed to increment visits after ${maxRetries} attempts:`,
            error
          );
          throw error;
        }

        // Wait with exponential backoff before retrying
        const backoffMs = Math.min(100 * Math.pow(2, retryCount), 1000);
        await new Promise((resolve) => setTimeout(resolve, backoffMs));

        console.warn(
          `Retrying database operation (${retryCount}/${maxRetries})`
        );
      }
    }

    // Only log in development
    if (process.env.NODE_ENV === 'development') {
      console.log(
        `Incremented visit for ${type} level ${level}, new count: ${category?.visits}, ID: ${visitId}`
      );
    }

    return NextResponse.json(
      { visits: category?.visits || 0, success: true, visitId },
      { headers }
    );
  } catch (error) {
    console.error('Error incrementing category visits:', error);
    return NextResponse.json(
      { error: 'Failed to increment category visits', success: false },
      { status: 500 }
    );
  }
}
