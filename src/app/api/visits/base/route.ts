import { NextRequest, NextResponse } from 'next/server';

import prisma from '@/lib/prisma';

export async function POST(req: NextRequest) {
  try {
    const { id } = await req.json();
    if (!id)
      return NextResponse.json({ error: 'Missing base id' }, { status: 400 });

    const base = await prisma.base.update({
      where: { id },
      data: { views: { increment: 1 } },
    });
    return NextResponse.json({ views: base.views });
  } catch (_error) {
    return NextResponse.json(
      { error: 'Failed to increment base views' },
      { status: 500 }
    );
    console.error(_error);
    // Optionally log the error for debugging
  }
}
