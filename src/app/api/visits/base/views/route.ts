import { NextRequest, NextResponse } from 'next/server';

import prisma from '@/lib/prisma';

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const id = searchParams.get('id');

  if (!id) {
    return NextResponse.json(
      { error: 'Missing base id parameter' },
      { status: 400 }
    );
  }

  try {
    const base = await prisma.base.findUnique({
      where: { id },
      select: { views: true },
    });

    if (!base) {
      return NextResponse.json({ error: 'Base not found' }, { status: 404 });
    }

    // Set strict no-cache headers to ensure we always get fresh data
    // This prevents any browser or CDN caching
    return NextResponse.json(
      { views: base.views },
      {
        headers: {
          'Cache-Control':
            'no-store, no-cache, must-revalidate, proxy-revalidate',
          Pragma: 'no-cache',
          Expires: '0',
        },
      }
    );
  } catch (error) {
    console.error('Error fetching base views:', error);
    return NextResponse.json(
      { error: 'Failed to fetch base views' },
      { status: 500 }
    );
  }
}
