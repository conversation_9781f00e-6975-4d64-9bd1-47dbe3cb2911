import { writeFile } from 'fs/promises';
import path from 'path';
import { NextRequest, NextResponse } from 'next/server';

import prisma from '@/lib/prisma';

// Use a string union for BaseType due to Prisma/TS bug
export type BaseType = 'TOWN_HALL' | 'BUILDER_HALL';

export async function POST(req: NextRequest) {
  const formData = await req.formData();
  const image = formData.get('image') as File | null;
  const title = formData.get('title') as string;
  const link = formData.get('link') as string;
  const level = parseInt(formData.get('level') as string, 10);

  if (!image || !title || !link || !level) {
    return NextResponse.json({ error: 'Missing fields' }, { status: 400 });
  }

  // Save image to public/uploads
  const bytes = await image.arrayBuffer();
  const buffer = Buffer.from(bytes);
  const filename = `${Date.now()}-${image.name}`;
  const uploadDir = path.join(process.cwd(), 'public', 'uploads');
  await writeFile(path.join(uploadDir, filename), buffer);
  const imageUrl = `/uploads/${filename}`;

  // Save to DB
  const base = await prisma.base.create({
    data: {
      title,
      baseLink: link,
      level,
      type: 'BUILDER_HALL',
      imageUrl,
    },
  });

  return NextResponse.json({ base });
}
