import { NextResponse } from 'next/server';

import prisma from '@/lib/prisma';

// Define the chart data point structure
interface ChartDataPoint {
  name: string;
  'Town Hall': number;
  'Builder Hall': number;
}

// Get page views for the last 30 days
export async function GET() {
  try {
    // For static export, only 'daily' is supported

    // Get the start date based on the period
    const startDate = new Date();
    // Only 'daily' supported for static export
    startDate.setDate(startDate.getDate() - 30); // Last 30 days

    // Since we don't have the BaseVisit model yet, we'll use the Base model to generate view data
    // First, let's get base data for Town Hall and Builder Hall bases
    const townHallBases = await prisma.base.findMany({
      where: {
        type: 'TOWN_HALL',
        createdAt: {
          gte: startDate,
        },
      },
      select: {
        id: true,
        createdAt: true,
        views: true,
        level: true,
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    const builderHallBases = await prisma.base.findMany({
      where: {
        type: 'BUILDER_HALL',
        createdAt: {
          gte: startDate,
        },
      },
      select: {
        id: true,
        createdAt: true,
        views: true,
        level: true,
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    // Now we'll aggregate the data based on the period
    const data: ChartDataPoint[] = [];
    const dateMap = new Map<string, ChartDataPoint>();

    // Function to format date based on the period
    const formatDateKey = (date: Date): string => {
      // Only 'daily' supported
      const month = date.toLocaleString('default', { month: 'short' });
      const day = date.getDate();
      return `${month} ${day}`;
    };

    // Process Town Hall bases
    townHallBases.forEach((base) => {
      const dateKey = formatDateKey(base.createdAt);
      if (!dateMap.has(dateKey)) {
        dateMap.set(dateKey, {
          name: dateKey,
          'Town Hall': 0,
          'Builder Hall': 0,
        });
      }
      const entry = dateMap.get(dateKey)!;
      entry['Town Hall'] += base.views;
    });

    // Process Builder Hall bases
    builderHallBases.forEach((base) => {
      const dateKey = formatDateKey(base.createdAt);
      if (!dateMap.has(dateKey)) {
        dateMap.set(dateKey, {
          name: dateKey,
          'Town Hall': 0,
          'Builder Hall': 0,
        });
      }
      const entry = dateMap.get(dateKey)!;
      entry['Builder Hall'] += base.views;
    });

    // Convert map to array and sort by date
    dateMap.forEach((value) => {
      data.push(value);
    });

    // Sort by date
    const sortedData = data.sort((a, b) => {
      // Simple heuristic for date comparison based on the formatted strings
      return a.name.localeCompare(b.name);
    });

    return NextResponse.json(sortedData);

    // Note: The above code with mockData replaces the need for this section.
    // When the BaseVisit table is implemented, we'll use the formatVisitsData function.

    // const formattedData = formatVisitsData(
    //   townHallVisits,
    //   builderHallVisits,
    //   period
    // );
    // return NextResponse.json(formattedData);
  } catch (error) {
    console.error('Error fetching page views:', error);
    return NextResponse.json(
      { error: 'Failed to fetch page views' },
      { status: 500 }
    );
  }
}

// The following functions are commented out as they're not currently used.
// They will be uncommented and used when the BaseVisit table is implemented.

/*
// Types for visit data
interface VisitCount {
  date: Date | string;
  count: number | string;
}

interface ChartDataPoint {
  name: string;
  'Town Hall': number;
  'Builder Hall': number;
}

function formatVisitsData(
  townHallVisits: VisitCount[],
  builderHallVisits: VisitCount[],
  period: string
): ChartDataPoint[] {
  const data: ChartDataPoint[] = [];
  const dateFormat =
    period === 'monthly'
      ? 'MMM YYYY'
      : period === 'weekly'
        ? 'MMM DD'
        : 'MMM DD';

  // Create a map of dates to make combining easier
  const dateMap = new Map<string, ChartDataPoint>();

  // Process town hall visits
  townHallVisits.forEach((visit) => {
    const date = new Date(visit.date);
    const formattedDate = formatDate(date, dateFormat);

    dateMap.set(formattedDate, {
      name: formattedDate,
      'Town Hall': Number(visit.count),
      'Builder Hall': 0,
    });
  });

  // Process builder hall visits
  builderHallVisits.forEach((visit) => {
    const date = new Date(visit.date);
    const formattedDate = formatDate(date, dateFormat);

    if (dateMap.has(formattedDate)) {
      const entry = dateMap.get(formattedDate)!;
      entry['Builder Hall'] = Number(visit.count);
    } else {
      dateMap.set(formattedDate, {
        name: formattedDate,
        'Town Hall': 0,
        'Builder Hall': Number(visit.count),
      });
    }
  });

  // Convert map to array
  dateMap.forEach((value) => {
    data.push(value);
  });

  // Sort by date
  return data.sort((a, b) => {
    return new Date(a.name).getTime() - new Date(b.name).getTime();
  });
}

// Helper function to format dates
function formatDate(date: Date, format: string): string {
  const months = [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
    'Nov',
    'Dec',
  ];
  const month = months[date.getMonth()];
  const day = date.getDate();
  const year = date.getFullYear();

  if (format === 'MMM YYYY') {
    return `${month} ${year}`;
  } else {
    return `${month} ${day}`;
  }
}
*/
