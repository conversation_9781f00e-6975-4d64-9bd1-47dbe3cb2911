import { NextResponse } from 'next/server';

import prisma from '@/lib/prisma';

export async function GET() {
  try {
    // Calculate metrics from the past 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    // Previous period for comparison (60-30 days ago)
    const sixtyDaysAgo = new Date();
    sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);

    // Since BaseVisit and BaseDownload tables don't exist in the database yet,
    // we'll use the Base table to get view counts as a proxy

    // Count visitors based on the Base model's views field for current period
    const currentBases = await prisma.base.findMany({
      where: {
        createdAt: {
          gte: thirtyDaysAgo,
        },
      },
      select: {
        views: true,
      },
    });

    const currentVisitors = currentBases.reduce(
      (total, base) => total + base.views,
      0
    );

    // Previous period
    const previousBases = await prisma.base.findMany({
      where: {
        createdAt: {
          gte: sixtyDaysAgo,
          lt: thirtyDaysAgo,
        },
      },
      select: {
        views: true,
      },
    });

    const previousVisitors = previousBases.reduce(
      (total, base) => total + base.views,
      0
    );

    // Get download counts from the Base model
    const currentBasesDownloads = await prisma.base.findMany({
      where: {
        createdAt: {
          gte: thirtyDaysAgo,
        },
      },
    });

    // Extract and sum the download counts
    const currentDownloads = currentBasesDownloads.reduce((total, base) => {
      // @ts-expect-error - Handle field name differences between environments
      const count = base.downloadCount ?? base.downloads ?? 0;
      return total + count;
    }, 0);

    // Previous period downloads
    const previousBasesDownloads = await prisma.base.findMany({
      where: {
        createdAt: {
          gte: sixtyDaysAgo,
          lt: thirtyDaysAgo,
        },
      },
    });

    // Extract and sum the download counts
    const previousDownloads = previousBasesDownloads.reduce((total, base) => {
      // @ts-expect-error - Handle field name differences between environments
      const count = base.downloadCount ?? base.downloads ?? 0;
      return total + count;
    }, 0); // Calculate changes
    const visitorChange = calculatePercentageChange(
      previousVisitors,
      currentVisitors
    );
    const downloadChange = calculatePercentageChange(
      previousDownloads,
      currentDownloads
    );

    // Dummy session data (would be calculated from real analytics in production)
    const avgSessionDuration = '3m 42s';
    const sessionChangePercent = '+5%';

    const bounceRate = '32%';
    const bounceChangePercent = '-3%';

    return NextResponse.json({
      metrics: [
        {
          title: 'Total Visitors',
          value: currentVisitors.toString(),
          change:
            visitorChange >= 0 ? `+${visitorChange}%` : `${visitorChange}%`,
        },
        {
          title: 'Avg. Session Duration',
          value: avgSessionDuration,
          change: sessionChangePercent,
        },
        {
          title: 'Bounce Rate',
          value: bounceRate,
          change: bounceChangePercent,
        },
        {
          title: 'Downloads',
          value: currentDownloads.toString(),
          change:
            downloadChange >= 0 ? `+${downloadChange}%` : `${downloadChange}%`,
        },
      ],
    });
  } catch (error) {
    console.error('Error fetching visitor metrics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch visitor metrics' },
      { status: 500 }
    );
  }
}

function calculatePercentageChange(previous: number, current: number): number {
  if (previous === 0) return current > 0 ? 100 : 0;
  const change = ((current - previous) / previous) * 100;
  return Math.round(change);
}
