import { NextResponse } from 'next/server';

import prisma from '@/lib/prisma';

export async function GET() {
  try {
    // Get counts by category from the Base table
    const categoryCounts = await prisma.base.groupBy({
      by: ['category'],
      _count: {
        id: true,
      },
      where: {
        type: 'TOWN_HALL', // Only applicable for town hall bases
      },
    });

    // Format the data for the chart
    const formattedData = categoryCounts.map((item) => ({
      name: formatCategoryName(String(item.category)),
      value: item._count.id,
    }));

    return NextResponse.json(formattedData);
  } catch (error) {
    console.error('Error fetching category stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch category stats' },
      { status: 500 }
    );
  }
}

function formatCategoryName(category: string): string {
  // Format the category enum values for display
  switch (category) {
    case 'WAR':
      return 'War';
    case 'FARMING':
      return 'Farming';
    case 'TROPHY':
      return 'Trophy';
    case 'HYBRID':
      return 'Hybrid';
    case 'OTHER':
      return 'Other';
    default:
      return 'Unknown';
  }
}
