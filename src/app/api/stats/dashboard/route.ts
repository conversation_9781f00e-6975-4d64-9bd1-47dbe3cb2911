import { NextResponse } from 'next/server';

import prisma from '@/lib/prisma';

export async function GET() {
  try {
    // Get counts of bases by type
    const townHallBaseCount = await prisma.base.count({
      where: {
        type: 'TOWN_HALL',
      },
    });

    const builderHallBaseCount = await prisma.base.count({
      where: {
        type: 'BUILDER_HALL',
      },
    });

    // Get total user count
    const userCount = await prisma.user.count();

    // Calculate total bases
    const totalBases = townHallBaseCount + builderHallBaseCount;

    // Get count of bases by category
    const baseCategoryCounts = await prisma.base.groupBy({
      by: ['category'],
      _count: {
        id: true,
      },
      where: {
        type: 'TOWN_HALL', // Categories only apply to town hall bases
      },
    });

    // Create constants for the base categories
    const WAR = 'WAR';
    const FARMING = 'FARMING';
    const TROPHY = 'TROPHY';
    const HYBRID = 'HYBRID';
    const OTHER = 'OTHER';

    type BaseCategory =
      | typeof WAR
      | typeof FARMING
      | typeof TROPHY
      | typeof HYBRID
      | typeof OTHER;

    // Initialize categoryStats with zeros
    const categoryStats: Record<BaseCategory, number> = {
      [WAR]: 0,
      [FARMING]: 0,
      [TROPHY]: 0,
      [HYBRID]: 0,
      [OTHER]: 0,
    };

    // Fill in actual counts from the database
    baseCategoryCounts.forEach((item) => {
      const category = item.category as BaseCategory;
      categoryStats[category] = item._count.id;
    });

    return NextResponse.json({
      totalBases,
      townHallBases: townHallBaseCount,
      builderHallBases: builderHallBaseCount,
      activeUsers: userCount,
      categories: categoryStats,
    });
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboard stats' },
      { status: 500 }
    );
  }
}
