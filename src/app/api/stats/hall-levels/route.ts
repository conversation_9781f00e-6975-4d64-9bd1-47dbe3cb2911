import { NextResponse } from 'next/server';

import prisma from '@/lib/prisma';

export async function GET() {
  try {
    // Get counts by hall level for town hall bases
    const townHallLevels = await prisma.base.groupBy({
      by: ['level'],
      _count: {
        id: true,
      },
      where: {
        type: 'TOWN_HALL',
      },
      orderBy: {
        level: 'desc',
      },
    });

    // Get counts by hall level for builder hall bases
    const builderHallLevels = await prisma.base.groupBy({
      by: ['level'],
      _count: {
        id: true,
      },
      where: {
        type: 'BUILDER_HALL',
      },
      orderBy: {
        level: 'desc',
      },
    });

    // Format the data for the chart
    const formattedData = [
      ...townHallLevels.map((item) => ({
        name: `TH${item.level}`,
        views: item._count.id,
      })),
      ...builderHallLevels.map((item) => ({
        name: `BH${item.level}`,
        views: item._count.id,
      })),
    ];

    // Sort by views in descending order
    formattedData.sort((a, b) => b.views - a.views);

    return NextResponse.json(formattedData);
  } catch (error) {
    console.error('Error fetching hall level stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch hall level stats' },
      { status: 500 }
    );
  }
}
