import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  const data = await request.formData();
  const username = data.get('username');
  const password = data.get('password');
  // Check against either USERNAME or EMAIL for backward compatibility
  if (
    (username === process.env.AUTH_USERNAME ||
      username === process.env.AUTH_EMAIL) &&
    password === process.env.AUTH_PASSWORD
  ) {
    // Set cookie for admin-auth
    return NextResponse.json(
      { success: true },
      {
        headers: {
          'Set-Cookie': `admin-auth=true; Path=/admin; HttpOnly; SameSite=Lax`,
        },
      }
    );
  }
  return NextResponse.json({
    success: false,
    error: 'Invalid username or password',
  });
}
