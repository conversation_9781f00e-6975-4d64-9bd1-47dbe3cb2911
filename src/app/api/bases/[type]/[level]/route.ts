import { BaseCategory, Prisma } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

import prisma from '@/lib/prisma';

export async function GET(
  request: NextRequest,
  { params }: { params: { type: string; level: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const sort = searchParams.get('sort') || 'newest';
    const category = searchParams.get('category');

    // Convert URL path params to database types
    let dbType: 'TOWN_HALL' | 'BUILDER_HALL' | undefined;
    if (params.type === 'town-hall') dbType = 'TOWN_HALL';
    else if (params.type === 'builder-hall') dbType = 'BUILDER_HALL';
    else
      return NextResponse.json({ error: 'Invalid base type' }, { status: 400 });

    const level = parseInt(params.level, 10);
    if (isNaN(level)) {
      return NextResponse.json({ error: 'Invalid level' }, { status: 400 });
    }

    // Build the where clause based on filters
    const where: Prisma.BaseWhereInput = {
      type: dbType,
      level,
    };

    // Only apply category filter for Town Hall bases
    if (dbType === 'TOWN_HALL' && category) {
      where.category = category as BaseCategory;
    }

    // Determine the order based on sort parameter
    let orderBy: Prisma.BaseOrderByWithRelationInput = {};
    switch (sort) {
      case 'newest':
        orderBy = { createdAt: 'desc' };
        break;
      case 'oldest':
        orderBy = { createdAt: 'asc' };
        break;
      case 'popular':
        orderBy = { views: 'desc' };
        break;
      default:
        orderBy = { createdAt: 'desc' };
    }

    // Execute query with filters
    const bases = await prisma.base.findMany({
      where,
      orderBy,
      select: {
        id: true,
        type: true,
        level: true,
        title: true,
        imageUrl: true,
        baseLink: true,
        views: true,
        createdAt: true,
        sortOrder: true,
        category: true,
      },
    });

    // Get the visit count for this category
    const categoryVisit = await prisma.categoryVisit.findFirst({
      where: {
        type: dbType,
        level,
      },
    });

    return NextResponse.json({
      bases,
      initialVisits: categoryVisit?.visits || 0,
    });
  } catch (error) {
    console.error('Error fetching bases:', error);
    return NextResponse.json(
      { error: 'Failed to fetch bases' },
      { status: 500 }
    );
  }
}
