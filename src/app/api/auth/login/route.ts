export const runtime = 'nodejs';
import jwt from 'jsonwebtoken';
import { NextResponse } from 'next/server';

const USERNAME = process.env.AUTH_USERNAME || 'yourusername';
const USER_EMAIL = process.env.AUTH_EMAIL || '<EMAIL>';
const USER_PASSWORD = process.env.AUTH_PASSWORD || 'yourpassword';
const JWT_SECRET = process.env.JWT_SECRET || 'supersecretkey';

export async function POST(req: Request) {
  let username, email, password;

  // Check content type to determine how to extract data
  const contentType = req.headers.get('content-type') || '';

  if (contentType.includes('multipart/form-data')) {
    // Handle FormData
    try {
      const formData = await req.formData();
      username = formData.get('username')?.toString();
      email = username; // For form data, we'll use username as email too
      password = formData.get('password')?.toString();
      console.log('Form login attempt:', { username });
    } catch (error) {
      console.error('Error parsing form data:', error);
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid form data',
        },
        { status: 400 }
      );
    }
  } else {
    // Handle JSON
    try {
      const data = await req.json();
      username = data.username;
      email = data.email || username;
      password = data.password;
    } catch (error) {
      console.error('Error parsing JSON:', error);
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid JSON',
        },
        { status: 400 }
      );
    }
  }

  if (
    username &&
    password &&
    (username === USERNAME || username === USER_EMAIL) &&
    password === USER_PASSWORD
  ) {
    const token = jwt.sign({ username, email }, JWT_SECRET, {
      expiresIn: '7d',
    });

    // Set both token and admin-auth cookies for compatibility
    return NextResponse.json(
      {
        success: true,
        message: 'Authentication successful',
        user: { username, email },
      },
      {
        headers: {
          'Set-Cookie': [
            `token=${token}; Path=/; HttpOnly; SameSite=Lax; Max-Age=${60 * 60 * 24 * 7}`,
            `admin-auth=true; Path=/admin; HttpOnly; SameSite=Lax; Max-Age=${60 * 60 * 24 * 7}`,
          ].join(', '),
        },
      }
    );
  }

  // Authentication failed
  return NextResponse.json(
    {
      success: false,
      error: 'Invalid username or password',
    },
    { status: 401 }
  );
}
