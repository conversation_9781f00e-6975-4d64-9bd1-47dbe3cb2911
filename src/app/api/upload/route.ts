import { NextRequest, NextResponse } from 'next/server';

import cloudinary from '@/lib/cloudinary';

interface CloudinaryUploadResult {
  secure_url: string;
  public_id?: string;
  version?: number;
  width?: number;
  height?: number;
  format?: string;
  resource_type?: string;
  created_at?: string;
  bytes?: number;
  type?: string;
  etag?: string;
  placeholder?: boolean;
  url?: string;
  original_filename?: string;
  // Add more fields as needed from Cloudinary response
}

export async function POST(req: NextRequest) {
  const formData = await req.formData();
  const image = formData.get('image') as File | null;
  if (!image) {
    return NextResponse.json({ error: 'No image provided' }, { status: 400 });
  }

  console.log('Upload request received:', {
    filename: image.name,
    type: image.type,
    size: image.size,
  });

  // Convert File to Buffer
  const arrayBuffer = await image.arrayBuffer();
  const buffer = Buffer.from(arrayBuffer);

  // Verify Cloudinary configuration
  if (!process.env.CLOUDINARY_URL) {
    console.error('Missing CLOUDINARY_URL environment variable');
    return NextResponse.json(
      { error: 'Cloudinary configuration missing' },
      { status: 500 }
    );
  }

  // Upload to Cloudinary
  try {
    console.log('Attempting Cloudinary upload...');
    const uploadResult = await new Promise<CloudinaryUploadResult>(
      (resolve, reject) => {
        cloudinary.uploader
          .upload_stream(
            {
              resource_type: 'image',
              folder: 'clash-of-clans-bases',
            },
            (err, result) => {
              if (err) {
                console.error('Cloudinary upload error:', err);
                return reject(err);
              }
              if (!result) {
                console.error('No result from Cloudinary');
                return reject(new Error('No result from Cloudinary'));
              }
              console.log('Upload successful:', result.secure_url);
              resolve(result);
            }
          )
          .end(buffer);
      }
    );
    return NextResponse.json({ secure_url: uploadResult.secure_url });
  } catch (e) {
    console.error('Cloudinary upload failed:', e);
    return NextResponse.json(
      { error: 'Cloudinary upload failed', details: String(e) },
      { status: 500 }
    );
  }
}
