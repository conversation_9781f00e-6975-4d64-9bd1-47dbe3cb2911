import { NextResponse } from 'next/server';

export async function POST() {
  // Clear multiple cookies to ensure proper logout
  const cookiesToClear = [
    'admin-auth=; Path=/admin; HttpOnly; Max-Age=0; SameSite=Lax',
    'token=; Path=/; HttpOnly; Max-Age=0; SameSite=Lax',
    'next-auth.session-token=; Path=/; HttpOnly; Max-Age=0; SameSite=Lax',
  ];

  return new NextResponse(
    JSON.stringify({ success: true, message: 'Logged out successfully' }),
    {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Set-Cookie': cookiesToClear.join(', '),
      },
    }
  );
}
