import { NextResponse } from 'next/server';

import prisma from '@/lib/prisma';

export async function GET() {
  const bases = await prisma.base.findMany({
    orderBy: { createdAt: 'desc' },
  });
  return NextResponse.json({ bases });
}

export async function DELETE(req: Request) {
  try {
    const { id } = await req.json();
    if (!id || typeof id !== 'string') {
      return NextResponse.json(
        { error: 'Missing or invalid id' },
        { status: 400 }
      );
    }
    await prisma.base.delete({ where: { id } });
    return NextResponse.json({ success: true });
  } catch {
    return NextResponse.json(
      { error: 'Failed to delete base' },
      { status: 500 }
    );
  }
}
