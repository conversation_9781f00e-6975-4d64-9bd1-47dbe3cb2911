import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';

import cloudinary from '@/lib/cloudinary';
import prisma from '@/lib/prisma';

export async function POST(req: NextRequest) {
  const formData = await req.formData();
  const type = formData.get('type');
  const level = Number(formData.get('level'));
  const image = formData.get('image') as File;
  const baseLink = formData.get('baseLink');
  const category = formData.get('category');

  // Different validation based on base type
  if (!type || !level || !image || !baseLink) {
    return NextResponse.json(
      { success: false, error: 'Missing fields' },
      { status: 400 }
    );
  }

  // For Town Hall bases, category is required
  if (type === 'TOWN_HALL' && !category) {
    return NextResponse.json(
      { success: false, error: 'Category is required for Town Hall bases' },
      { status: 400 }
    );
  }

  // Upload image to Cloudinary directly
  let imageUrl: string;

  try {
    console.log('Starting direct Cloudinary upload...');

    // Convert file to buffer
    const arrayBuffer = await image.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Upload directly to Cloudinary
    const uploadResult = await new Promise<{ secure_url: string }>(
      (resolve, reject) => {
        cloudinary.uploader
          .upload_stream(
            {
              resource_type: 'image',
              folder: 'clash-of-clans-bases',
              format: 'jpg', // Force jpg format
              transformation: [
                { quality: 'auto:good' }, // Optimize quality
                { fetch_format: 'auto' }, // Auto format for best delivery
              ],
            },
            (error, result) => {
              if (error || !result) {
                console.error('Cloudinary upload error:', error);
                return reject(error || new Error('No result from Cloudinary'));
              }
              console.log('Cloudinary upload successful:', result.secure_url);
              resolve(result);
            }
          )
          .end(buffer);
      }
    );

    imageUrl = uploadResult.secure_url;
  } catch (error) {
    console.error('Image upload error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Image upload to Cloudinary failed',
        details: String(error),
      },
      { status: 500 }
    );
  }

  // Define the base data type
  type BaseData = {
    id: string;
    type: 'TOWN_HALL' | 'BUILDER_HALL';
    level: number;
    imageUrl: string;
    baseLink: string;
    category?: 'WAR' | 'FARMING' | 'TROPHY' | 'HYBRID' | 'OTHER';
    views?: number;
    createdAt?: string;
  };

  // Prepare base data
  const baseData: BaseData = {
    id: uuidv4(),
    type: type as 'TOWN_HALL' | 'BUILDER_HALL',
    level,
    imageUrl,
    baseLink: baseLink as string,
  };

  // Only include category for Town Hall bases, or use OTHER as default for Builder Hall
  if (type === 'TOWN_HALL' && category) {
    const categoryEnum = (category as string).toUpperCase();
    baseData.category = categoryEnum as
      | 'WAR'
      | 'FARMING'
      | 'TROPHY'
      | 'HYBRID'
      | 'OTHER';
  } else {
    baseData.category = 'OTHER'; // Default for Builder Hall
  }

  // Store in DB
  const base = await prisma.base.create({
    data: baseData,
  });

  return NextResponse.json({ success: true, base });
}
