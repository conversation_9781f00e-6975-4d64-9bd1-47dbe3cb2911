'use client';

import { useRef, useState } from 'react';
import Image from 'next/image';

import CategoryCardClient from '@/components/CategoryCardClient';
import HomeBuilderHallSection from '@/components/home-builderhall-section';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface HomeClientProps {
  stats: Array<{ thLevel: number; count: number; views: number }>;
  builderHallStats: Array<{ bhLevel: number; count: number; views: number }>;
  totalBases: number;
}

export default function HomeClient({
  stats,
  builderHallStats,
  totalBases,
}: HomeClientProps) {
  const [activeTab, setActiveTab] = useState<'townHalls' | 'builderHalls'>(
    'townHalls'
  );
  const tabsRef = useRef<HTMLDivElement>(null);

  // Scroll to tabs and set tab
  const handleTabButtonClick = (tab: 'townHalls' | 'builderHalls') => {
    setActiveTab(tab);
    setTimeout(() => {
      if (tabsRef.current) {
        tabsRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }, 50);
  };

  return (
    <>
      <section
        className="relative flex h-full items-center justify-center bg-cover bg-center text-white lg:h-[37vw]"
        style={{
          backgroundImage:
            "linear-gradient(to right, rgba(0,0,0,0.55), rgba(0,0,0,0.60)), url('/images/hero-bg-main.webp')",
        }}
      >
        <div className="container mx-auto px-4 py-24">
          <div className="flex flex-col gap-12 lg:flex-row">
            {/* Left Column */}
            <div className="flex w-full flex-col items-start justify-center space-y-8 md:w-3/5">
              <div className="inline-flex rounded-full bg-amber-900/30 px-4 py-1.5 text-sm font-medium text-amber-400">
                More than {totalBases.toLocaleString()}+ Bases for each Town
                Hall & Builder Hall
              </div>

              <h2 className="font-supercell text-4xl font-bold tracking-tight md:text-5xl lg:text-6xl">
                <span className="block">Best Clash of Clans</span>
                <span className="bg-gradient-to-r from-amber-400 to-yellow-200 bg-clip-text text-transparent">
                  Bases 2025 - [Updated]
                </span>
              </h2>

              <p className="text-base text-white">
                Effortlessly copy the latest Clash of Clans base links with just
                a few taps, whether for war, farming, or trophy pushing.
                Discover clans that perfectly match your playstyle and
                preferences, making it easier to find the right community. Need
                fresh recruits? Quickly find new, active players to strengthen
                your clan for wars, leagues, and clan games.
              </p>

              <div className="flex flex-wrap gap-4 pt-4">
                <button
                  type="button"
                  className="group flex items-center gap-2 rounded-md bg-amber-500 px-6 py-3 font-medium text-black shadow-lg transition hover:bg-amber-400"
                  onClick={() => handleTabButtonClick('townHalls')}
                >
                  View Townhall Bases
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="lucide lucide-arrow-right size-4 -rotate-45 transition-all ease-out group-hover:ml-3 group-hover:rotate-0"
                    aria-hidden="true"
                  >
                    <path d="M5 12h14"></path>
                    <path d="m12 5 7 7-7 7"></path>
                  </svg>
                </button>
                <button
                  type="button"
                  className="group flex items-center gap-2 rounded-md border border-white/20 bg-white/10 px-6 py-3 font-medium text-white backdrop-blur transition hover:bg-white/20"
                  onClick={() => handleTabButtonClick('builderHalls')}
                >
                  View Builderhall Bases
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="lucide lucide-arrow-right size-4 -rotate-45 transition-all ease-out group-hover:ml-3 group-hover:rotate-0"
                    aria-hidden="true"
                  >
                    <path d="M5 12h14"></path>
                    <path d="m12 5 7 7-7 7"></path>
                  </svg>
                </button>
              </div>
            </div>
            {/* ...existing right column and rest of the page... */}
            <div className="relative w-full md:w-2/5">
              {/* Main Featured Image */}
              <div className="relative z-10 h-full rounded-lg border border-amber-500/30 bg-gradient-to-br from-amber-900/30 to-black p-3 shadow-2xl">
                <div className="h-full overflow-hidden rounded-md">
                  <Image
                    priority
                    src="/images/hero-base.jpg"
                    alt="War Base Strategy"
                    width={2400}
                    height={1080}
                    className="size-full object-cover transition-transform duration-700 hover:scale-105"
                  />
                </div>
                <div className="absolute -bottom-3 right-6 rounded-full bg-amber-500 px-4 py-1 text-sm font-bold text-black">
                  FEATURED STRATEGY
                </div>
              </div>
              {/* Decorative Elements */}
              <div className="absolute -bottom-8 -left-8 hidden size-40 rounded-full bg-amber-500/10 blur-3xl md:block"></div>
              <div className="absolute -right-10 top-1/3 hidden size-40 rounded-full bg-red-500/10 blur-3xl md:block"></div>
              {/* Small cards */}
              <div className="absolute left-5 top-1/4 z-20 max-w-[160px] rounded-lg bg-black/80 p-3 shadow-xl backdrop-blur-sm md:-left-5">
                <div className="text-sm font-semibold text-amber-400">
                  War Win Rate
                </div>
                <h4 className="text-2xl font-bold">94%</h4>
                <div className="text-xs text-gray-400">with our top bases</div>
              </div>
              <div className="absolute -bottom-6 left-6 z-20 max-w-[180px] rounded-lg bg-black/80 p-3 shadow-xl backdrop-blur-sm">
                <div className="flex items-center gap-2">
                  <div className="flex size-10 items-center justify-center rounded-full bg-amber-500/20">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 24 24"
                      width="24"
                      height="24"
                      color="#fbbf24"
                      fill="none"
                    >
                      <path
                        d="M4 11H20V22H4V11Z"
                        stroke="#fbbf24"
                        strokeWidth="1.5"
                        strokeLinejoin="round"
                      ></path>
                      <path
                        d="M14.5 22V19C14.5 18.0572 14.5 17.5858 14.2071 17.2929C13.9142 17 13.4428 17 12.5 17H11.5C10.5572 17 10.0858 17 9.79289 17.2929C9.5 17.5858 9.5 18.0572 9.5 19V22"
                        stroke="#fbbf24"
                        strokeWidth="1.5"
                        strokeLinejoin="round"
                      ></path>
                      <path
                        d="M2 9.72254C2 9.14501 2.26952 8.68201 2.81725 8.49897L10.9302 5.78773C11.7893 5.50062 12 5.0255 12 4.18608C12 3.42891 11.8761 1.9173 13.0641 2.00228C13.3438 2.02229 13.6832 2.28692 14.3619 2.81619L21.439 8.33464C21.8381 8.64581 22 9.01714 22 9.53489C22 10.4781 21.6036 11 20.6848 11H3.14677C2.40983 11 2 10.4554 2 9.72254Z"
                        stroke="#fbbf24"
                        strokeWidth="1.5"
                        strokeLinejoin="round"
                      ></path>
                      <path
                        d="M3 22H21"
                        stroke="#fbbf24"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      ></path>
                      <path
                        d="M7 15H8"
                        stroke="#fbbf24"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                      ></path>
                      <path
                        d="M17 15L16 15"
                        stroke="#fbbf24"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                      ></path>
                      <path
                        d="M5 7.5L5 3"
                        stroke="#fbbf24"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      ></path>
                    </svg>
                  </div>
                  <div>
                    <div className="text-sm font-semibold">Best Layouts</div>
                    <div className="text-xs text-gray-400">
                      {totalBases.toLocaleString()}+ and growing
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* ...existing content... */}

      <div className="container mx-auto py-8 md:py-24">
        <div className="container grid grid-cols-1 items-center gap-8 md:gap-14 lg:grid-cols-2 lg:gap-20">
          <div className="flex-1">
            <h1 className="font-supercell max-w-160 text-3xl tracking-tight md:text-4xl lg:text-5xl">
              Dominate with our expert base layouts
            </h1>
            <p className="text-muted-foreground mt-5 text-lg">
              Unlock unbeatable strategies with handpicked bases designed for
              war, trophy pushing, farming, and hybrid defense.
            </p>

            <div className="mt-8 flex flex-wrap items-center gap-4 lg:flex-nowrap">
              <button className="focus-visible:outline-hidden focus-visible:ring-ring bg-primary text-primary-foreground hover:bg-primary/90 inline-flex h-9 items-center justify-center gap-2 whitespace-nowrap rounded-lg px-4 py-2 text-sm font-semibold shadow-sm transition-colors focus-visible:ring-1 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0">
                View Bases
              </button>
            </div>
          </div>
          <div className="relative flex flex-1 flex-col justify-center space-y-5 max-lg:pt-10 lg:ps-10">
            <div className="text-muted-foreground absolute left-0 top-0 h-full w-px max-lg:hidden">
              <div className="h-full w-px bg-[repeating-linear-gradient(180deg,transparent,transparent_4px,currentColor_4px,currentColor_10px)] [mask-image:linear-gradient(180deg,transparent,black_25%,black_75%,transparent)]"></div>
            </div>
            <div className="text-muted-foreground absolute top-0 h-px w-full lg:hidden">
              <div className="h-px w-full bg-[repeating-linear-gradient(90deg,transparent,transparent_4px,currentColor_4px,currentColor_10px)] [mask-image:linear-gradient(90deg,transparent,black_25%,black_75%,transparent)]"></div>
            </div>
            <div className="flex gap-2.5 lg:gap-5">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="lucide lucide-circle-dot text-primary mt-1 size-4 shrink-0 lg:size-5"
              >
                <circle cx="12" cy="12" r="10"></circle>
                <circle cx="12" cy="12" r="1"></circle>
              </svg>
              <div>
                <h2 className="font-supercell font-text font-semibold">
                  Base Categories
                </h2>
                <p className="text-muted-foreground max-w-76 text-sm">
                  Find the perfect layout for war, farming, or trophies.
                </p>
              </div>
            </div>
            <div className="flex gap-2.5 lg:gap-5">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="lucide lucide-blend text-primary mt-1 size-4 shrink-0 lg:size-5"
              >
                <circle cx="9" cy="9" r="7"></circle>
                <circle cx="15" cy="15" r="7"></circle>
              </svg>
              <div>
                <h2 className="font-supercell font-text font-semibold">
                  Clan Tools
                </h2>
                <p className="text-muted-foreground max-w-76 text-sm">
                  Share and manage layouts across your clan for better war
                  coordination and defense.
                </p>
              </div>
            </div>
            <div className="flex gap-2.5 lg:gap-5">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="lucide lucide-diamond text-primary mt-1 size-4 shrink-0 lg:size-5"
              >
                <path d="M2.7 10.3a2.41 2.41 0 0 0 0 3.41l7.59 7.59a2.41 2.41 0 0 0 3.41 0l7.59-7.59a2.41 2.41 0 0 0 0-3.41l-7.59-7.59a2.41 2.41 0 0 0-3.41 0Z"></path>
              </svg>
              <div>
                <h2 className="font-supercell font-text font-semibold">
                  Town Hall Progression
                </h2>
                <p className="text-muted-foreground max-w-76 text-sm">
                  Explore layouts specific to each Town Hall level from TH3 to
                  TH17 and plan your upgrade path.
                </p>
              </div>
            </div>
            <div className="flex gap-2.5 lg:gap-5">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="lucide lucide-chart-no-axes-column text-primary mt-1 size-4 shrink-0 lg:size-5"
              >
                <line x1="18" x2="18" y1="20" y2="10"></line>
                <line x1="12" x2="12" y1="20" y2="4"></line>
                <line x1="6" x2="6" y1="20" y2="14"></line>
              </svg>
              <div>
                <h2 className="font-supercell font-text font-semibold">
                  Base Insights
                </h2>
                <p className="text-muted-foreground max-w-76 text-sm">
                  Track how well each layout performs based on community
                  feedback and win/loss stats.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto">
        <div className="relative mt-6 flex items-center justify-center">
          <div className="text-muted-foreground relative h-px w-full">
            <div className="h-px w-full bg-[repeating-linear-gradient(90deg,transparent,transparent_4px,currentColor_4px,currentColor_10px)] [mask-image:linear-gradient(90deg,transparent,black_25%,black_75%,transparent)]"></div>
          </div>
          <h3 className="bg-muted text-muted-foreground absolute px-3 text-sm font-medium tracking-wide max-md:hidden">
            COPY. APPLY. DOMINATE.
          </h3>
        </div>
      </div>

      <section className="container py-8 md:py-24" ref={tabsRef}>
        <h2 className="font-supercell mb-8 text-center text-4xl font-bold text-black md:text-5xl dark:text-white">
          Click the switches to explore bases
        </h2>
        <Tabs
          defaultValue="townHalls"
          value={activeTab}
          onValueChange={(val) =>
            setActiveTab(val as 'townHalls' | 'builderHalls')
          }
          className="text-muted-foreground"
        >
          <div className="mb-8 flex justify-center">
            <TabsList
              className="grid max-w-3xl grid-cols-2 rounded-full border border-amber-500/30 bg-white/90 p-1.5 shadow-lg dark:bg-black/40"
              size="lg"
              variant="button"
              shape="pill"
            >
              <TabsTrigger
                value="townHalls"
                className="font-supercell py-3 text-base font-medium text-amber-900 data-[state=active]:bg-black data-[state=active]:text-white dark:text-amber-100 dark:data-[state=active]:bg-amber-600"
              >
                Town Halls
              </TabsTrigger>
              <TabsTrigger
                value="builderHalls"
                className="font-supercell py-3 text-base font-medium text-amber-900 data-[state=active]:bg-black data-[state=active]:text-white dark:text-amber-100 dark:data-[state=active]:bg-amber-600"
              >
                Builder Halls
              </TabsTrigger>
            </TabsList>
          </div>

          {/* Prerender both tab contents but hide the inactive one with CSS */}
          <div className="relative">
            <TabsContent
              value="townHalls"
              forceMount
              className="data-[state=inactive]:hidden"
            >
              <div className="grid grid-cols-2 gap-6 sm:grid-cols-3 md:grid-cols-4">
                {stats.map(({ thLevel, count, views }) => (
                  <CategoryCardClient
                    key={thLevel}
                    type="TOWN_HALL"
                    level={thLevel}
                    count={count}
                    views={views}
                    href={`/base/town-hall/${thLevel}`}
                    label={`TH${thLevel} Bases (Town Hall ${thLevel})`}
                    baseLabel="base"
                  />
                ))}
              </div>
            </TabsContent>
            <TabsContent
              value="builderHalls"
              forceMount
              className="data-[state=inactive]:hidden"
            >
              <HomeBuilderHallSection stats={builderHallStats} />
            </TabsContent>
          </div>
        </Tabs>
      </section>
    </>
  );
}
