import { ImageResponse } from 'next/og';

import { siteConfig } from '@/lib/constant';

// Route segment config
export const runtime = 'edge';

// Image metadata
export const alt = 'Clash of Clans Bases';
export const size = {
  width: 1200,
  height: 630,
};

export const contentType = 'image/png';

// Image generation
export default async function Image() {
  // For Twitter, we'll use the same image as OpenGraph
  return new ImageResponse(
    (
      <div
        style={{
          fontSize: 128,
          background: 'linear-gradient(to bottom, #0072b1, #000)',
          width: '100%',
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          padding: '40px',
        }}
      >
        <img
          src={`${siteConfig.url()}/images/logo.png`}
          alt="Logo"
          width={200}
          height={200}
          style={{ marginBottom: '20px' }}
        />
        <div
          style={{
            fontSize: '60px',
            fontWeight: 'bold',
            textAlign: 'center',
          }}
        >
          Clash of Clans Bases
        </div>
        <div
          style={{
            fontSize: '32px',
            marginTop: '20px',
            textAlign: 'center',
          }}
        >
          Find the best bases for Town Hall & Builder Hall
        </div>
      </div>
    ),
    {
      ...size,
    }
  );
}
