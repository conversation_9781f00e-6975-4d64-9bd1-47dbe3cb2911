import { ImageResponse } from 'next/og';

// Route segment config
export const runtime = 'edge';

// Image metadata
export const alt = 'Clash of Clans Bases';
export const size = {
  width: 1200,
  height: 630,
};

export const contentType = 'image/png';

// Image generation
export default async function Image() {
  // You can use any library or method to generate the image
  // This is using the built-in ImageResponse from Next.js
  return new ImageResponse(
    (
      <div
        style={{
          fontSize: 128,
          background: 'linear-gradient(to bottom, #0072b1, #000)',
          width: '100%',
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          padding: '40px',
        }}
      >
        {/* Using a text-based logo instead of an image to ensure compatibility with WhatsApp */}
        <div
          style={{
            fontSize: '80px',
            fontWeight: 'bold',
            textAlign: 'center',
            marginBottom: '20px',
            textShadow: '0 2px 10px rgba(0,0,0,0.4)',
            color: '#ffcc00',
          }}
        >
          CoC Bases
        </div>
        <div
          style={{ fontSize: '60px', fontWeight: 'bold', textAlign: 'center' }}
        >
          Clash of Clans Bases
        </div>
        <div
          style={{ fontSize: '32px', marginTop: '20px', textAlign: 'center' }}
        >
          Find the best bases for Town Hall & Builder Hall
        </div>
      </div>
    ),
    {
      ...size,
    }
  );
}
