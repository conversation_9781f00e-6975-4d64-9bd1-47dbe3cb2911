'use client';
import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { useSession } from 'next-auth/react';

import LoginForm from '@/components/auth/LoginForm';

export default function LoginPage() {
  const searchParams = useSearchParams();
  const role = searchParams.get('role') || 'user';
  const { status } = useSession();
  const [isAdmin, setIsAdmin] = useState(role === 'admin');

  // Check if the user is already logged in
  useEffect(() => {
    if (status === 'authenticated') {
      window.location.href = isAdmin ? '/admin' : '/';
    }
  }, [status, isAdmin]);

  // Determine if we're in admin mode based on the query param or URL path
  useEffect(() => {
    const checkIfAdmin = () => {
      // Check for explicit role parameter
      if (searchParams.get('role') === 'admin') {
        return true;
      }

      // Check URL path for admin-related paths
      const pathname = window.location.pathname;
      if (pathname.includes('/admin')) {
        return true;
      }

      return false;
    };

    setIsAdmin(checkIfAdmin());
  }, [searchParams]);

  // Log analytics data
  useEffect(() => {
    console.info(
      `Login page accessed with role: ${isAdmin ? 'admin' : 'user'}`
    );
    console.info(`Referrer: ${document.referrer || 'direct'}`);
  }, [isAdmin]);

  // Configure login props based on user type
  const loginConfig = isAdmin
    ? {
        role: 'admin' as const,
        title: 'Admin Login',
        submitText: 'Sign in to Admin',
        redirectPath: '/admin',
        apiRoute: '/api/admin-login',
        showOAuthProviders: false,
        showRememberMe: true,
      }
    : {
        role: 'user' as const,
        title: 'Sign in to GameBase',
        submitText: 'Sign in',
        redirectPath: '/',
        apiRoute: '/api/auth/login',
        showOAuthProviders: true,
        showRememberMe: true,
      };

  // Show appropriate background based on login type
  const backgroundStyle = isAdmin
    ? {
        className:
          'flex min-h-screen items-center justify-center bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700',
      }
    : {
        className:
          'flex min-h-screen items-center justify-center bg-gradient-to-br from-gray-900 via-gray-800 to-gray-700',
      };

  return (
    <div {...backgroundStyle}>
      <LoginForm {...loginConfig} />
    </div>
  );
}
