import HomeClient from './HomeClient';

import { getBuilderHallStats } from '@/lib/builderhall';
import { getTownHallStats } from '@/lib/townhall';
export default async function Home() {
  // Only show Town Halls 3 to 17
  const stats = (await getTownHallStats()).filter(
    ({ thLevel }) => thLevel >= 3 && thLevel <= 17
  );

  // Get Builder Hall stats
  const builderHallStats = await getBuilderHallStats();

  // Calculate total bases across all Town Hall and Builder Hall levels
  const townHallBasesCount = stats.reduce((sum, { count }) => sum + count, 0);
  const builderHallBasesCount = builderHallStats.reduce(
    (sum, { count }) => sum + count,
    0
  );
  const totalBases = townHallBasesCount + builderHallBasesCount;

  return (
    <HomeClient
      stats={stats}
      builderHallStats={builderHallStats}
      totalBases={totalBases}
    />
  );
}
// ...existing code...
