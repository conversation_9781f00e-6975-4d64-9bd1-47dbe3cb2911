'use client';
import { useEffect } from 'react';
import { useSession } from 'next-auth/react';

export default function SignInPage() {
  const { status } = useSession();

  // Immediately redirect to the new unified login page
  useEffect(() => {
    // If already authenticated, go to home
    if (status === 'authenticated') {
      window.location.href = '/';
    } else {
      // Otherwise redirect to the new login page
      window.location.href = '/login?role=user';
    }
  }, [status]);

  // This return is just a placeholder while redirecting
  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-gray-900 via-gray-800 to-gray-700">
      <div className="w-full max-w-sm rounded-xl bg-white/90 p-8 shadow-2xl backdrop-blur-md">
        <h2 className="font-supercell mb-8 text-center text-3xl font-extrabold text-gray-900">
          Redirecting to login...
        </h2>
        <div className="flex justify-center">
          <div className="size-8 animate-spin rounded-full border-y-2 border-blue-500"></div>
        </div>
      </div>
    </div>
  );
}
