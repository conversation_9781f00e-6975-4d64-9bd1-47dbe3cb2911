import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import AdminBasesClient from './_components/AdminBasesClient';
import AdminDashboardHeader from './_components/AdminDashboardHeader';
import { BaseRow } from './_components/BaseTable';
import AddBaseForm from './add-base-form';

import prisma from '@/lib/prisma';
import ReduxProvider from '@/store/Provider';

function AdminStats({
  builderHallCount,
  townHallCount,
  totalBases,
}: {
  builderHallCount: number;
  townHallCount: number;
  totalBases: number;
}) {
  return (
    <div className="mt-3 grid grid-cols-1 gap-6 md:grid-cols-3">
      <a
        href="/admin/dashboard"
        className="bg-card hover:bg-accent/50 rounded-lg border p-6 text-center shadow transition-colors"
      >
        <div className="text-2xl font-bold">{builderHallCount}</div>
        <div className="text-muted-foreground mt-2 text-sm font-medium">
          Builder Hall Bases
        </div>
      </a>
      <a
        href="/admin/dashboard"
        className="bg-card hover:bg-accent/50 rounded-lg border p-6 text-center shadow transition-colors"
      >
        <div className="text-2xl font-bold">{townHallCount}</div>
        <div className="text-muted-foreground mt-2 text-sm font-medium">
          Town Hall Bases
        </div>
      </a>
      <a
        href="/admin/dashboard"
        className="bg-card hover:bg-accent/50 rounded-lg border p-6 text-center shadow transition-colors"
      >
        <div className="text-2xl font-bold">{totalBases}</div>
        <div className="text-muted-foreground mt-2 text-sm font-medium">
          Total Bases Submitted
        </div>
      </a>
    </div>
  );
}

export default async function AdminDashboardPage() {
  // Check for admin-auth cookie
  const cookieStore = await cookies();
  const isAdmin = cookieStore.get('admin-auth')?.value === 'true';
  if (!isAdmin) {
    redirect('/admin/login');
  }
  const builderHallCount = await prisma.base.count({
    where: { type: 'BUILDER_HALL' },
  });
  const townHallCount = await prisma.base.count({
    where: { type: 'TOWN_HALL' },
  });
  const totalBases = builderHallCount + townHallCount;

  const bases = await prisma.base.findMany({
    orderBy: { createdAt: 'desc' },
  });
  // Explicitly type b as Base from Prisma
  const baseRows: BaseRow[] = bases.map(
    (b: {
      id: string;
      imageUrl: string;
      type: 'TOWN_HALL' | 'BUILDER_HALL';
      level: number;
      views: number;
      createdAt: Date | string;
      baseLink?: string | null;
      category?: 'WAR' | 'FARMING' | 'TROPHY' | 'HYBRID' | 'OTHER' | null; // Using proper enum values
    }) => ({
      id: b.id,
      imageUrl: b.imageUrl,
      type: b.type,
      level: b.level,
      views: b.views,
      createdAt:
        b.createdAt instanceof Date ? b.createdAt.toISOString() : b.createdAt,
      baseLink: b.baseLink,
      // Handle both string and enum cases safely
      category: (b.category || 'OTHER') as
        | 'WAR'
        | 'FARMING'
        | 'TROPHY'
        | 'HYBRID'
        | 'OTHER',
    })
  );
  return (
    <ReduxProvider>
      <main className="mx-auto mt-16 max-w-7xl rounded-xl bg-white/90 p-8 shadow-2xl backdrop-blur-md">
        <AdminDashboardHeader title="Admin Dashboard" />
        {/* Client-only interactive content */}
        <AddBaseForm />
        {/* Server-rendered stats */}
        <AdminStats
          builderHallCount={builderHallCount}
          townHallCount={townHallCount}
          totalBases={totalBases}
        />
        {/* Client-rendered table with Redux integration */}
        <AdminBasesClient initialBases={baseRows} />
      </main>
    </ReduxProvider>
  );
}
