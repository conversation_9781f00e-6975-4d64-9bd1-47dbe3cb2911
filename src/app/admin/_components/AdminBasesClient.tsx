'use client';

import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import BaseTable from '../_components/BaseTable';

import { RootState } from '@/store';
import { Base, setBases } from '@/store/baseSlice';

// Define valid categories
type CategoryType = 'WAR' | 'FARMING' | 'TROPHY' | 'HYBRID' | 'OTHER';

// Define the props interface
interface AdminBasesClientProps {
  initialBases: Base[];
}

// Client component with Redux integration
export default function AdminBasesClient({
  initialBases,
}: AdminBasesClientProps) {
  const dispatch = useDispatch();
  const bases = useSelector((state: RootState) => state.base.bases);

  // Initialize Redux store with server data
  useEffect(() => {
    if (initialBases.length > 0 && bases.length === 0) {
      dispatch(setBases(initialBases));
    }
  }, [dispatch, initialBases, bases.length]);

  // Transform Base objects to BaseRow objects (adding default category if needed)
  const transformToBaseRows = (basesArray: Base[]) => {
    return basesArray.map((base) => {
      // Get category from base or default to 'OTHER'
      let category: CategoryType = 'OTHER';
      if (base.type === 'TOWN_HALL') {
        const baseWithCategory = base as { category?: CategoryType };
        if (
          baseWithCategory.category &&
          ['WAR', 'FARMING', 'TROPHY', 'HYBRID', 'OTHER'].includes(
            baseWithCategory.category
          )
        ) {
          category = baseWithCategory.category;
        }
      }

      return {
        ...base,
        category,
      };
    });
  };

  // Use the Redux store data for the table
  return (
    <BaseTable
      bases={transformToBaseRows(bases.length > 0 ? bases : initialBases)}
    />
  );
}
