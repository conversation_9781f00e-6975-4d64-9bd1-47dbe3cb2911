'use client';

import { toast } from 'sonner';

interface AdminDashboardHeaderProps {
  title: string;
}

export default function AdminDashboardHeader({
  title,
}: AdminDashboardHeaderProps) {
  // No need for router as we're using window.location.href

  // Function to handle admin logout
  const handleLogout = async () => {
    try {
      // Show loading toast
      const toastId = toast.loading('Logging out...');

      const response = await fetch('/api/admin-logout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        // Update toast to success
        toast.success('Successfully logged out', {
          id: toastId,
          duration: 2000,
        });

        // Short delay to allow the toast to be seen
        setTimeout(() => {
          // Redirect to login page after successful logout using a full page reload
          window.location.href = '/login';
        }, 800);
      } else {
        const errorText = await response.text();
        // Update toast to error
        toast.error(`Logout failed: ${errorText || 'Unknown error'}`, {
          id: toastId,
          duration: 4000,
        });
      }
    } catch (error) {
      toast.error(
        `Error during logout: ${error instanceof Error ? error.message : 'Unknown error'}`,
        {
          duration: 4000,
        }
      );
    }
  };

  return (
    <div className="mb-8 flex items-center justify-between">
      <h1 className="text-3xl font-extrabold text-gray-900">{title}</h1>
      <button
        className="bg-secondary text-secondary-foreground hover:bg-secondary/80 inline-flex items-center justify-center whitespace-nowrap rounded-md px-4 py-2 text-sm font-medium shadow-sm"
        onClick={handleLogout}
      >
        Logout
      </button>
    </div>
  );
}
