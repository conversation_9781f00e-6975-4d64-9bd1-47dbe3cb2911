'use client';
import { useState } from 'react';
import { Check, Trash2, X } from 'lucide-react';
import Image from 'next/image';

import { Button } from '@/components/ui/button';

export type BaseRow = {
  id: string;
  imageUrl: string;
  type: 'TOWN_HALL' | 'BUILDER_HALL';
  level: number;
  views: number;
  createdAt: string;
  baseLink?: string | null;
  category: 'WAR' | 'FARMING' | 'TROPHY' | 'HYBRID' | 'OTHER';
};

interface BaseTableProps {
  bases: BaseRow[];
  onDelete?: (id: string) => void;
}

export default function BaseTable({ bases, onDelete }: BaseTableProps) {
  const [deleting, setDeleting] = useState<string | null>(null);

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this base?')) return;
    setDeleting(id);
    try {
      const res = await fetch(`/api/base/${id}`, { method: 'DELETE' });
      if (!res.ok) throw new Error('Failed to delete base');
      if (onDelete) onDelete(id);
    } catch (e) {
      console.error('Error deleting base', e);
    } finally {
      setDeleting(null);
    }
  };

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full border text-sm">
        <thead>
          <tr className="bg-gray-100">
            <th className="p-2">ID</th>
            <th className="p-2">Image</th>
            <th className="p-2">Type</th>
            <th className="p-2">Level</th>
            <th className="p-2">Category</th>
            <th className="p-2">Views</th>
            <th className="p-2">Created</th>
            <th className="p-2">Link</th>
            <th className="p-2">Actions</th>
          </tr>
        </thead>
        <tbody>
          {bases.map((base) => (
            <tr key={base.id} className="border-b">
              <td className="p-2 font-mono text-xs">{base.id}</td>
              <td className="p-2">
                <Image
                  src={base.imageUrl}
                  alt={base.id}
                  width={64}
                  height={64}
                  className="rounded object-cover"
                />
              </td>
              <td className="p-2">
                {base.type === 'TOWN_HALL' ? 'Town Hall' : 'Builder Hall'}
              </td>
              <td className="p-2">{base.level}</td>
              <td className="p-2">
                <span
                  className={`rounded-full px-2 py-1 text-xs font-medium ${
                    base.category === 'WAR'
                      ? 'bg-red-100 text-red-800'
                      : base.category === 'FARMING'
                        ? 'bg-green-100 text-green-800'
                        : base.category === 'TROPHY'
                          ? 'bg-yellow-100 text-yellow-800'
                          : base.category === 'HYBRID'
                            ? 'bg-purple-100 text-purple-800'
                            : 'bg-gray-100 text-gray-800'
                  }`}
                >
                  {base.category.charAt(0) +
                    base.category.slice(1).toLowerCase()}
                </span>
              </td>
              <td className="p-2">{base.views}</td>
              <td className="p-2">
                {new Intl.DateTimeFormat('en', {
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric',
                }).format(new Date(base.createdAt))}
              </td>
              <td className="p-2 text-center">
                {base.baseLink ? (
                  <Check className="inline text-green-600" />
                ) : (
                  <X className="inline text-red-600" />
                )}
              </td>
              <td className="p-2">
                <Button
                  variant="destructive"
                  size="icon"
                  disabled={deleting === base.id}
                  onClick={() => handleDelete(base.id)}
                  aria-label="Delete base"
                >
                  <Trash2 />
                </Button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
