'use client';

import { useEffect, useState } from 'react';

import { StatsTab } from '@/components/dashboard/StatsTab';
import {
  Card,
  Dashboard,
  DashboardHeader,
  DashboardSection,
} from '@/components/ui/dashboard';
import { StatsCard, StatsGrid } from '@/components/ui/stats-card';

export default function AdminDashboardPage() {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <Dashboard>
      <DashboardHeader
        title="Admin Dashboard"
        description="Monitor and manage your CoC Bases platform"
        actions={
          <button
            className="bg-secondary text-secondary-foreground hover:bg-secondary/80 inline-flex items-center justify-center whitespace-nowrap rounded-md px-4 py-2 text-sm font-medium shadow-sm"
            onClick={() => {
              // Implement logout functionality
              window.location.href = '/api/auth/signout';
            }}
          >
            Logout
          </button>
        }
      />

      <div className="flex border-b">
        <button
          className={`px-4 py-2 ${
            activeTab === 'overview'
              ? 'border-primary text-primary border-b-2'
              : 'text-muted-foreground'
          }`}
          onClick={() => setActiveTab('overview')}
        >
          Overview
        </button>
        <button
          className={`px-4 py-2 ${
            activeTab === 'bases'
              ? 'border-primary text-primary border-b-2'
              : 'text-muted-foreground'
          }`}
          onClick={() => setActiveTab('bases')}
        >
          Bases
        </button>
        <button
          className={`px-4 py-2 ${
            activeTab === 'users'
              ? 'border-primary text-primary border-b-2'
              : 'text-muted-foreground'
          }`}
          onClick={() => setActiveTab('users')}
        >
          Users
        </button>
        <button
          className={`px-4 py-2 ${
            activeTab === 'stats'
              ? 'border-primary text-primary border-b-2'
              : 'text-muted-foreground'
          }`}
          onClick={() => setActiveTab('stats')}
        >
          Statistics
        </button>
      </div>

      {activeTab === 'overview' && <OverviewTab />}
      {activeTab === 'bases' && <BasesTab />}
      {activeTab === 'users' && <UsersTab />}
      {activeTab === 'stats' && <StatsTab />}
    </Dashboard>
  );
}

function OverviewTab() {
  const [stats, setStats] = useState({
    totalBases: '...',
    activeUsers: '...',
    townHallBases: '...',
    builderHallBases: '...',
  });

  const [loading, setLoading] = useState(true);

  // Fetch dashboard stats
  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetch('/api/stats/dashboard');
        const data = await response.json();
        setStats({
          totalBases: data.totalBases.toLocaleString(),
          activeUsers: data.activeUsers.toLocaleString(),
          townHallBases: data.townHallBases.toLocaleString(),
          builderHallBases: data.builderHallBases.toLocaleString(),
        });
      } catch (error) {
        console.error('Error fetching dashboard stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  return (
    <>
      <DashboardSection>
        <StatsGrid>
          <StatsCard
            title="Total Bases"
            value={stats.totalBases}
            change={{ value: loading ? '...' : '+12.3%', trend: 'up' }}
            description="Compared to last month"
          />
          <StatsCard
            title="Active Users"
            value={stats.activeUsers}
            change={{ value: loading ? '...' : '+8.7%', trend: 'up' }}
          />
          <StatsCard
            title="Town Hall Bases"
            value={stats.townHallBases}
            change={{ value: loading ? '...' : '+10.1%', trend: 'up' }}
          />
          <StatsCard
            title="Builder Hall Bases"
            value={stats.builderHallBases}
            change={{ value: loading ? '...' : '+5.4%', trend: 'up' }}
          />
        </StatsGrid>
      </DashboardSection>

      <DashboardSection className="grid-cols-1 lg:grid-cols-2">
        <Card
          title="Recent Activity"
          description="The latest actions on the platform"
        >
          <div className="space-y-4">
            {[1, 2, 3, 4].map((i) => (
              <div
                key={i}
                className="flex items-center gap-4 rounded-md border p-3"
              >
                <div className="bg-accent size-10 rounded-full"></div>
                <div>
                  <h4 className="font-medium">New TH11 War Base Added</h4>
                  <p className="text-muted-foreground text-sm">
                    Added by User123 • 2 hours ago
                  </p>
                </div>
              </div>
            ))}
          </div>
        </Card>
        <Card title="Top Categories" description="Most popular base categories">
          <div className="space-y-4">
            {['Defense', 'War', 'Trophy', 'Farming'].map((category) => (
              <div
                key={category}
                className="flex items-center justify-between rounded-md border p-3"
              >
                <div className="font-medium">{category}</div>
                <div className="text-muted-foreground">
                  {Math.floor(Math.random() * 200 + 100)} bases
                </div>
              </div>
            ))}
          </div>
        </Card>
      </DashboardSection>
    </>
  );
}

function BasesTab() {
  return (
    <DashboardSection>
      <Card>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">All Bases</h3>
            <div className="flex gap-2">
              <button className="bg-primary text-primary-foreground inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1.5 text-sm font-medium shadow-sm">
                Add New
              </button>
              <div className="search-input max-w-sm">
                <input type="text" placeholder="Search bases..." />
              </div>
            </div>
          </div>

          <div className="rounded-md border">
            <div className="bg-muted grid grid-cols-5 border-b p-3 text-sm font-medium">
              <div>Name</div>
              <div>Type</div>
              <div>Category</div>
              <div>Added</div>
              <div>Actions</div>
            </div>
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div
                key={i}
                className="grid grid-cols-5 items-center border-b p-3 text-sm last:border-0"
              >
                <div className="font-medium">War Base {i}</div>
                <div>
                  {Math.random() > 0.3 ? 'Town Hall' : 'Builder Hall'}{' '}
                  {Math.floor(Math.random() * 5 + 10)}
                </div>
                <div>
                  {
                    ['War', 'Defense', 'Trophy', 'Farming'][
                      Math.floor(Math.random() * 4)
                    ]
                  }
                </div>
                <div className="text-muted-foreground">
                  {new Date(
                    Date.now() - Math.random() * 10000000000
                  ).toLocaleDateString()}
                </div>
                <div className="flex gap-2">
                  <button className="bg-secondary text-secondary-foreground inline-flex items-center justify-center whitespace-nowrap rounded-md px-2 py-1 text-xs font-medium shadow-sm">
                    Edit
                  </button>
                  <button className="bg-destructive text-destructive-foreground inline-flex items-center justify-center whitespace-nowrap rounded-md px-2 py-1 text-xs font-medium shadow-sm">
                    Delete
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Card>
    </DashboardSection>
  );
}

function UsersTab() {
  return (
    <DashboardSection>
      <Card>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">All Users</h3>
            <div className="search-input max-w-sm">
              <input type="text" placeholder="Search users..." />
            </div>
          </div>

          <div className="rounded-md border">
            <div className="bg-muted grid grid-cols-4 border-b p-3 text-sm font-medium">
              <div>Name</div>
              <div>Email</div>
              <div>Role</div>
              <div>Actions</div>
            </div>
            {[1, 2, 3, 4, 5].map((i) => (
              <div
                key={i}
                className="grid grid-cols-4 items-center border-b p-3 text-sm last:border-0"
              >
                <div className="font-medium">User {i}</div>
                <div>user{i}@example.com</div>
                <div>
                  {Math.random() > 0.8
                    ? 'Admin'
                    : Math.random() > 0.5
                      ? 'Editor'
                      : 'User'}
                </div>
                <div className="flex gap-2">
                  <button className="bg-secondary text-secondary-foreground inline-flex items-center justify-center whitespace-nowrap rounded-md px-2 py-1 text-xs font-medium shadow-sm">
                    Edit
                  </button>
                  <button className="bg-destructive text-destructive-foreground inline-flex items-center justify-center whitespace-nowrap rounded-md px-2 py-1 text-xs font-medium shadow-sm">
                    Suspend
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Card>
    </DashboardSection>
  );
}
