'use client';
import { useCallback, useState } from 'react';
import { useDispatch } from 'react-redux';
import Image from 'next/image';
import { toast } from 'sonner';

import { LevelRadioGroup } from '@/components/ui/LevelRadioGroup';
import {
  RadioCardGroup,
  RadioCardOption,
} from '@/components/ui/RadioCardGroup';
import { addBase } from '@/store/baseSlice';

export type BaseType = 'BUILDER_HALL' | 'TOWN_HALL';

interface AddBaseFormProps {
  onSuccess?: () => void;
}

const BASE_CATEGORIES = [
  { value: 'WAR', label: 'War' },
  { value: 'FARMING', label: 'Farming' },
  { value: 'TROPHY', label: 'Trophy' },
  { value: 'HYBRID', label: 'Hybrid' },
  { value: 'OTHER', label: 'Other' },
];
const BASE_CATEGORY_OPTIONS: RadioCardOption[] = BASE_CATEGORIES.map((cat) => ({
  value: cat.value,
  label: cat.label,
}));

export default function AddBaseForm({ onSuccess }: AddBaseFormProps) {
  const [type, setType] = useState<BaseType>('TOWN_HALL');
  const [level, setLevel] = useState(3); // default to min valid
  const [image, setImage] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [baseLink, setBaseLink] = useState('');
  const [category, setCategory] = useState('WAR');
  const dispatch = useDispatch();

  // BaseTypeSwitch component with direct state handling and accessibility support
  const BaseTypeSwitch = () => {
    // Handler for changing the base type
    const handleTypeChange = (newType: BaseType) => {
      setType(newType);
      setLevel(3); // Reset level when type changes
    };

    return (
      <div className="flex flex-wrap gap-2">
        {/* Use proper radio buttons wrapped in labels for accessibility */}
        <div className="flex items-center">
          <input
            type="radio"
            id="town-hall-option"
            name="base-type-radio"
            value="TOWN_HALL"
            checked={type === 'TOWN_HALL'}
            onChange={() => handleTypeChange('TOWN_HALL')}
            className="sr-only" // Visually hidden but accessible
          />
          <label
            htmlFor="town-hall-option"
            className={`inline-flex h-10 cursor-pointer items-center justify-center rounded-md px-4 text-sm font-medium transition-colors ${
              type === 'TOWN_HALL'
                ? 'bg-primary text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600'
            }`}
          >
            Town Hall
          </label>
        </div>

        <div className="flex items-center">
          <input
            type="radio"
            id="builder-hall-option"
            name="base-type-radio"
            value="BUILDER_HALL"
            checked={type === 'BUILDER_HALL'}
            onChange={() => handleTypeChange('BUILDER_HALL')}
            className="sr-only" // Visually hidden but accessible
          />
          <label
            htmlFor="builder-hall-option"
            className={`inline-flex h-10 cursor-pointer items-center justify-center rounded-md px-4 text-sm font-medium transition-colors ${
              type === 'BUILDER_HALL'
                ? 'bg-primary text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600'
            }`}
          >
            Builder Hall
          </label>
        </div>
      </div>
    );
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setImage(file);
    if (file) {
      const reader = new FileReader();
      reader.onload = () => setPreview(reader.result as string);
      reader.readAsDataURL(file);
    } else {
      setPreview(null);
    }
  };

  // Dynamic level options
  const levelOptions =
    type === 'TOWN_HALL'
      ? Array.from({ length: 15 }, (_, i) => i + 3) // 3-17
      : Array.from({ length: 8 }, (_, i) => i + 3); // 3-10

  const resetForm = useCallback(() => {
    // Reset all form fields to their default values
    setType('TOWN_HALL');
    setLevel(3);
    setImage(null);
    setPreview(null);
    setBaseLink('');
    setCategory('WAR');

    // Reset the file input element
    const fileInput = document.getElementById(
      'image-upload'
    ) as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  }, []);

  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();
      setError('');

      // Different validation based on base type
      if (type === 'TOWN_HALL') {
        if (!type || !image || !baseLink || !category) {
          setError(
            'Please select a base type, image, category, and enter a base link.'
          );
          return;
        }
      } else {
        // For Builder Hall, category is not required
        if (!type || !image || !baseLink) {
          setError('Please select a base type, image, and enter a base link.');
          return;
        }
      }
      setLoading(true);
      const formData = new FormData();
      formData.append('type', type);
      formData.append('level', String(level));
      formData.append('baseLink', baseLink);
      formData.append('category', category);
      if (image) formData.append('image', image);
      // Show loading toast
      const toastId = toast.loading('Uploading base...');

      try {
        const res = await fetch('/api/admin/add-base', {
          method: 'POST',
          body: formData,
        });

        const data = await res.json();

        if (data.success && data.base) {
          // Update the Redux store with the new base
          dispatch(addBase(data.base));

          // Reset the form using our dedicated function
          resetForm();

          // Show success toast
          toast.success('Base added successfully!', {
            id: toastId,
            description: `${type} level ${level} base has been added.`,
          });

          // Call any additional success callback
          onSuccess?.();
        } else {
          // Show error toast
          toast.error(data.error || 'Failed to add base', {
            id: toastId,
            description: 'Please try again or contact support.',
          });
          setError(data.error || 'Failed to add base');
        }
      } catch (err) {
        // Handle unexpected errors
        console.error('Error adding base:', err);
        toast.error('Failed to add base', {
          id: toastId,
          description: 'An unexpected error occurred. Please try again.',
        });
        setError('An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    },
    [type, level, image, baseLink, category, onSuccess, dispatch, resetForm]
  );

  return (
    <form
      onSubmit={handleSubmit}
      className="mx-auto mt-8 flex max-w-7xl flex-col gap-6 rounded bg-white p-6 shadow-md"
    >
      <div className="font-medium">
        Base for:
        <div className="mt-2">
          <BaseTypeSwitch />
        </div>
      </div>
      <div className="font-medium">
        Level:
        <LevelRadioGroup
          name="base-level"
          levels={levelOptions}
          value={level}
          onChange={setLevel}
        />
      </div>
      <label className="font-medium" htmlFor="image-upload">
        Image Upload:
        <input
          id="image-upload"
          type="file"
          accept="image/*"
          onChange={handleImageChange}
          required
          className="mt-1 block"
        />
      </label>
      <label className="font-medium" htmlFor="base-link">
        Base Link:
        <input
          id="base-link"
          type="url"
          value={baseLink}
          onChange={(e) => setBaseLink(e.target.value)}
          required
          placeholder="https://..."
          className="mt-1 block w-full rounded border p-2"
        />
      </label>
      {/* Only show category selection for Town Hall bases */}
      {type === 'TOWN_HALL' && (
        <div className="font-medium">
          Category:
          <div className="mt-2">
            <RadioCardGroup
              name="base-category"
              options={BASE_CATEGORY_OPTIONS}
              value={category}
              onChange={setCategory}
            />
          </div>
        </div>
      )}
      {preview && (
        <div className="mb-2">
          <span className="mb-1 block text-xs text-gray-500">Preview:</span>
          <Image
            src={typeof preview === 'string' ? preview : ''}
            alt="Preview"
            width={160}
            height={160}
            className="max-h-40 rounded border object-cover"
            style={{ maxHeight: 160, width: 'auto', height: 'auto' }}
          />
        </div>
      )}
      <button
        type="submit"
        disabled={loading}
        className="bg-primary rounded p-2 text-white disabled:opacity-60"
      >
        {loading ? 'Submitting...' : 'Submit'}
      </button>
      {error && <div className="mt-1 text-sm text-red-600">{error}</div>}
    </form>
  );
}
