import { redirect } from 'next/navigation';

export default async function TownHallLevelPage({
  params,
}: {
  params: { th: string };
}) {
  const { th } = params;

  // Validate that the parameter is a number between 3-17
  const level = parseInt(th, 10);
  if (isNaN(level) || level < 3 || level > 17) {
    // Redirect to the homepage if the level is invalid
    return redirect('/');
  }

  // Redirect to the standardized route for town hall bases
  return redirect(`/base/town-hall/${level}`);
}

// Generate metadata for this page
export function generateMetadata({ params }: { params: { th: string } }) {
  const level = parseInt(params.th, 10);
  const isValidLevel = !isNaN(level) && level >= 3 && level <= 17;

  return {
    title: isValidLevel
      ? `TH${level} Bases - Town Hall ${level} Base Layouts - Clash of Clans`
      : 'Clash of Clans Base Layouts',
    description: isValidLevel
      ? `Find the best Town Hall ${level} (TH${level}) base designs for Clash of Clans. Trophy, war, and farming layouts.`
      : 'Find the best base layouts for Clash of Clans. Trophy, war, and farming designs for all Town Hall levels.',
  };
}
