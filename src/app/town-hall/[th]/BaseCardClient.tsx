'use client';
import { useEffect, useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';

import { formatDate, formatViewCount } from '@/lib/utils';

export default function BaseCardClient({
  base,
}: {
  base: {
    id: string;
    type: string;
    level: number;
    imageUrl: string;
    baseLink?: string | null | undefined;
    views?: number;
    createdAt: Date;
    createdAtFormatted?: string;
    title?: string | null | undefined;
  };
}) {
  const [copied, setCopied] = useState(false);
  const [localViews, setLocalViews] = useState<number>(base.views ?? 0);

  // Update views when the base prop changes
  useEffect(() => {
    if (typeof base.views === 'number') {
      setLocalViews(base.views);
    }
  }, [base.views]);

  return (
    <Link
      href={`/base/town-hall/${base.level}/base/${base.id}`}
      className="flex items-center gap-4 rounded-lg border p-4 transition hover:shadow-lg"
      role="button"
      tabIndex={0}
      style={{ cursor: 'pointer' }}
      prefetch={true}
    >
      <Image
        src={base.imageUrl}
        alt={base.title || `Base ${base.id}`}
        width={128}
        height={128}
        className="size-32 rounded object-cover"
      />
      <div className="flex-1">
        <div className="text-lg font-semibold">
          {base.title || `TH${base.level} Base`}
        </div>
        <div className="mb-2 text-sm text-gray-500">Type: {base.type}</div>
        <div className="mb-1 text-xs text-gray-400">
          Added:{' '}
          {base.createdAtFormatted ||
            (base.createdAt ? formatDate(base.createdAt) : '')}
        </div>
        <div className="flex items-center gap-2">
          <button
            type="button"
            className="btn btn-sm btn-outline"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              navigator.clipboard.writeText(base.baseLink ?? '');
              setCopied(true);
              setTimeout(() => setCopied(false), 1200);
            }}
          >
            {copied ? 'Copied!' : 'Copy Link'}
          </button>
          <a
            href={base.baseLink || ''}
            target="_blank"
            rel="noopener noreferrer"
            className="btn btn-sm btn-primary"
            onClick={(e) => e.stopPropagation()}
          >
            Open in Clash
          </a>
          <span className="text-xs text-gray-400">
            👁️ {formatViewCount(localViews)}
          </span>
        </div>
      </div>
    </Link>
  );
}
