'use client';

import { useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

export default function WhatsAppShare() {
  const router = useRouter();
  const absoluteUrl = 'https://clashofclans-bases.vercel.app';
  const imageUrl = `${absoluteUrl}/opengraph-image.png`;

  // Redirect to homepage after a short delay
  useEffect(() => {
    const timer = setTimeout(() => {
      router.push('/');
    }, 5000);

    return () => clearTimeout(timer);
  }, [router]);

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="mx-auto max-w-3xl text-center">
        <h1 className="mb-6 text-3xl font-bold">WhatsApp Sharing Page</h1>
        <div className="mb-8">
          <div className="relative h-[300px] w-full">
            <div
              className="mx-auto size-full max-w-full rounded-md border bg-cover bg-center shadow-md"
              style={{ backgroundImage: `url(${imageUrl})` }}
              role="img"
              aria-label="Clash of Clans Bases"
            ></div>
          </div>
        </div>
        <p className="mb-4 text-lg">
          This page is optimized for sharing on WhatsApp. You will be redirected
          to the homepage in a few seconds.
        </p>
        <Link
          href="/"
          className="inline-block rounded-lg bg-blue-600 px-6 py-3 font-medium text-white transition-colors hover:bg-blue-700"
        >
          Go to Homepage Now
        </Link>
      </div>
    </div>
  );
}
