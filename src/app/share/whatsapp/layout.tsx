import { Metadata } from 'next';

// Define metadata specifically for WhatsApp sharing
export const metadata: Metadata = {
  title: 'Share on WhatsApp | Clash of Clans Bases',
  description: 'Best Town Hall & Builder Hall base layouts for Clash of Clans.',
  openGraph: {
    title: 'Clash of Clans Bases',
    description:
      'Best Town Hall & Builder Hall base layouts for Clash of Clans.',
    url: 'https://clashofclans-bases.vercel.app/share/whatsapp',
    siteName: 'Clash of Clans Bases',
    images: [
      {
        url: 'https://clashofclans-bases.vercel.app/opengraph-image.png',
        width: 1200,
        height: 630,
        alt: 'Clash of Clans Bases',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
};

export default function WhatsAppShareLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
