// Use a string union for BaseType due to Prisma/TS bug
export type BaseType = 'TOWN_HALL' | 'BUILDER_HALL';

import Image from 'next/image';
import Link from 'next/link';
import { notFound } from 'next/navigation';

import { CategoryViewManager } from '@/components/analytics/CategoryViewManager';
import prisma from '@/lib/prisma';
import { formatDate, formatViewCount } from '@/lib/utils';

type BuilderBase = {
  id: string;
  image: string;
  title: string;
  type: string;
  createdAt: string;
  views: number;
};

export const dynamic = 'force-dynamic';

export default async function BuilderHallPage({
  params,
  searchParams,
}: {
  params: { bh: string };
  searchParams: { sort?: string };
}) {
  const bhLevel = parseInt(params.bh, 10);
  if (isNaN(bhLevel) || bhLevel < 1 || bhLevel > 10) return notFound();

  const sort = searchParams.sort || 'newest';

  const orderBy =
    sort === 'oldest'
      ? { createdAt: 'asc' as const }
      : sort === 'popular'
        ? { views: 'desc' as const }
        : { createdAt: 'desc' as const };

  const basesRaw = await prisma.base.findMany({
    where: {
      type: 'BUILDER_HALL',
      level: bhLevel,
    },
    orderBy,
  });

  // Get category visit count
  const categoryVisit = await prisma.categoryVisit.findUnique({
    where: { type_level: { type: 'BUILDER_HALL', level: bhLevel } },
  });
  const initialVisits = categoryVisit?.visits ?? 0;

  // Map Prisma fields to display fields
  const bases: BuilderBase[] = basesRaw.map(
    (b: {
      id: string;
      imageUrl: string;
      type: string;
      level: number;
      createdAt: string | Date;
      title?: string | null;
      views: number;
    }) => {
      const withTitle = b as typeof b & { title?: string };
      return {
        id: b.id,
        image: b.imageUrl,
        title: withTitle.title ?? `BH${b.level} Base`,
        type: b.type,
        createdAt:
          b.createdAt instanceof Date ? b.createdAt.toISOString() : b.createdAt,
        views: b.views || 0,
      };
    }
  );

  console.log('Params', params);
  console.log('Search Params', searchParams);

  return (
    <main className="mx-auto max-w-3xl py-24">
      <h1 className="mb-6 text-2xl font-bold">
        Builder Hall {bhLevel} Bases{' '}
        <span className="text-sm font-normal text-gray-500">
          (
          <CategoryViewManager
            type="BUILDER_HALL"
            level={bhLevel}
            initialVisits={initialVisits}
          />{' '}
          views)
        </span>
      </h1>
      <div className="grid-col-1 grid gap-6 md:grid-cols-3">
        {bases.length === 0 && (
          <div>No bases found for this Builder Hall level.</div>
        )}
        {bases.map((base: BuilderBase) => (
          <Link
            key={base.id}
            href={`/base/builder-hall/${bhLevel}/base/${base.id}`}
            className="flex items-center gap-4 rounded-lg border p-4 transition hover:shadow-lg"
            role="button"
            tabIndex={0}
            style={{ cursor: 'pointer' }}
            prefetch={true}
          >
            <Image
              src={base.image}
              alt={base.title}
              width={128}
              height={128}
              className="size-32 rounded object-cover"
              loading="lazy"
              unoptimized
            />
            <div className="flex-1">
              <div className="text-lg font-semibold">{base.title}</div>
              <div className="mb-2 text-sm text-gray-500">
                Type: {base.type}
              </div>
              <div className="mb-1 text-xs text-gray-400">
                Added: {formatDate(base.createdAt)}
              </div>
              <div className="mb-1 text-xs text-blue-600">
                Views: {formatViewCount(base.views)}
              </div>
            </div>
          </Link>
        ))}
      </div>
    </main>
  );
}
