'use server';

import { BaseCategory, BaseType as PrismaBaseType } from '@prisma/client';

import prisma from '@/lib/prisma';

// Use a string union for BaseType due to Prisma/TS bug
export type BaseType = 'TOWN_HALL' | 'BUILDER_HALL';

export async function addBase(formData: {
  title: string;
  link: string;
  image: string;
  type: BaseType;
  level: number;
  category?: BaseCategory;
}) {
  const newBase = await prisma.base.create({
    data: {
      title: formData.title,
      baseLink: formData.link,
      imageUrl: formData.image,
      type: formData.type as PrismaBaseType,
      level: formData.level,
      // If category is provided, use it; otherwise, Prisma will use the default value (OTHER)
      ...(formData.category ? { category: formData.category } : {}),
    },
  });

  return newBase;
}
