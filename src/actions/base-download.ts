'use server';

import prisma from '@/lib/prisma';

/**
 * Increment the download count for a base
 * @param baseId The ID of the base to increment the download count for
 */
export async function incrementBaseDownloadCount(
  baseId: string
): Promise<void> {
  try {
    // Use raw SQL to update the downloadCount field
    await prisma.$executeRaw`UPDATE "Base" SET "downloadCount" = "downloadCount" + 1 WHERE id = ${baseId}`;
  } catch (error) {
    console.error('Failed to update download count:', error);
    throw new Error('Failed to update download count');
  }
}
