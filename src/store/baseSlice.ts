import { BaseCategory } from '@prisma/client';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface Base {
  id: string;
  type: 'BUILDER_HALL' | 'TOWN_HALL';
  level: number;
  title?: string | null;
  imageUrl: string;
  baseLink?: string | null; // Made optional to match Prisma schema
  views: number;
  createdAt: string;
  sortOrder?: number;
  category?: BaseCategory;
}

interface BaseState {
  bases: Base[];
}

const initialState: BaseState = { bases: [] };

const baseSlice = createSlice({
  name: 'base',
  initialState,
  reducers: {
    setBases(state, action: PayloadAction<Base[]>) {
      state.bases = action.payload;
    },
    addBase(state, action: PayloadAction<Base>) {
      state.bases.unshift(action.payload);
    },
  },
});

export const { setBases, addBase } = baseSlice.actions;
export default baseSlice.reducer;
