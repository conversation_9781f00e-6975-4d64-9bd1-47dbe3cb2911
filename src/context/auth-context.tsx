'use client';
import { createContext, useContext, useEffect, useState } from 'react';

interface AuthContextType {
  isLoggedIn: boolean;
  setIsLoggedIn: (v: boolean) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [isLoggedIn, setIsLoggedInState] = useState(false);

  useEffect(() => {
    // Check localStorage for auth state on mount
    const local = localStorage.getItem('isLoggedIn');
    if (local === 'true') {
      setIsLoggedInState(true);
    } else {
      setIsLoggedInState(false);
    }
  }, []);

  const setIsLoggedIn = (v: boolean) => {
    setIsLoggedInState(v);
    localStorage.setItem('isLoggedIn', v ? 'true' : 'false');
  };

  return (
    <AuthContext.Provider value={{ isLoggedIn, setIsLoggedIn }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const ctx = useContext(AuthContext);
  if (!ctx) throw new Error('useAuth must be used within AuthProvider');
  return ctx;
}
