/**
 * Clash of Clans Base Filter Context
 *
 * This context manages the global state for base filtering across the application.
 * It provides optimized filter state management with React Context to prevent
 * re-renders and ensure single-click responsiveness.
 */

import {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';

// Types
export type BaseType = 'town-hall' | 'builder-hall';
export type BaseCategory = 'ALL' | 'WAR' | 'TROPHY' | 'FARMING' | 'HYBRID';
export type SortOption = 'newest' | 'oldest' | 'popular';

interface FilterState {
  baseType: BaseType;
  level: number;
  category?: BaseCategory;
  sortBy: SortOption;
}

interface FilterContextValue extends FilterState {
  isLoading: boolean;
  updateFilters: (updates: Partial<FilterState>) => void;
  resetFilters: () => void;
}

// Default values
const defaultFilters: FilterState = {
  baseType: 'town-hall',
  level: 17,
  category: 'ALL',
  sortBy: 'newest',
};

// Create context
const FilterContext = createContext<FilterContextValue | undefined>(undefined);

// Provider component
export function FilterProvider({
  children,
  initialFilters,
}: {
  children: ReactNode;
  initialFilters?: Partial<FilterState>;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Merge default filters with any initial filters
  const mergedDefaults = { ...defaultFilters, ...initialFilters };

  // State
  const [filters, setFilters] = useState<FilterState>(mergedDefaults);
  const [isLoading, setIsLoading] = useState(false);

  // Navigate to the appropriate URL based on filter state
  const navigateToFilterUrl = useCallback(
    (newFilters: FilterState) => {
      setIsLoading(true);

      const basePath = '/base';
      const typePath = newFilters.baseType;
      const levelPath = newFilters.level.toString();

      // Build query parameters
      const params = new URLSearchParams();

      if (newFilters.sortBy) {
        params.set('sort', newFilters.sortBy);
      }

      // Only add category for town-hall bases and when not ALL
      if (
        newFilters.baseType === 'town-hall' &&
        newFilters.category &&
        newFilters.category !== 'ALL'
      ) {
        params.set('category', newFilters.category);
      }

      // Construct the URL
      const newUrl = `${basePath}/${typePath}/${levelPath}${params.toString() ? '?' + params.toString() : ''}`;

      // Navigate
      router.push(newUrl, { scroll: false });

      // Reset loading state after navigation
      setTimeout(() => setIsLoading(false), 300);
    },
    [router]
  );

  // Update filters and navigate
  const updateFilters = useCallback(
    (updates: Partial<FilterState>) => {
      // Skip if already loading
      if (isLoading) return;

      setFilters((currentFilters) => {
        // Special handling when changing baseType
        if (updates.baseType && updates.baseType !== currentFilters.baseType) {
          const newFilters = {
            ...currentFilters,
            ...updates,
            // Reset level to default when changing base type
            level: updates.baseType === 'town-hall' ? 17 : 10,
            // Ensure category is properly set when switching base types
            category:
              updates.baseType === 'builder-hall'
                ? undefined
                : currentFilters.category || 'ALL',
          };

          // Navigate after state update
          setTimeout(() => navigateToFilterUrl(newFilters), 0);
          return newFilters;
        }

        // Normal update for other filter changes
        const newFilters = { ...currentFilters, ...updates };

        // Navigate after state update
        setTimeout(() => navigateToFilterUrl(newFilters), 0);
        return newFilters;
      });
    },
    [isLoading, navigateToFilterUrl]
  );

  // Reset filters to defaults
  const resetFilters = useCallback(() => {
    // Skip if already loading
    if (isLoading) return;

    setFilters({
      baseType: 'town-hall',
      level: 17,
      category: 'ALL',
      sortBy: 'popular',
    });

    // Navigate to default URL
    setTimeout(() => {
      // Don't include the category parameter when it's 'ALL'
      const resetUrl = '/base/town-hall/17?sort=popular';
      router.push(resetUrl, { scroll: false });

      // Reset loading after navigation
      setTimeout(() => setIsLoading(false), 300);
    }, 0);

    setIsLoading(true);
  }, [isLoading, router]);

  // Sync URL parameters to state
  useEffect(() => {
    // Extract path parameters
    const pathSegments = pathname?.split('/') || [];

    if (pathSegments.length >= 3) {
      // Format: /base/[type]/[level]
      if (pathSegments[1] === 'base') {
        const typeFromPath = pathSegments[2] as BaseType;
        const levelFromPath = parseInt(pathSegments[3], 10);

        // Extract query parameters
        const categoryFromQuery = searchParams?.get('category') as
          | BaseCategory
          | undefined;
        const sortFromQuery = searchParams?.get('sort') as
          | SortOption
          | undefined;

        // Update state without triggering navigation
        setFilters((current) => ({
          ...current,
          baseType:
            typeFromPath === 'town-hall' || typeFromPath === 'builder-hall'
              ? typeFromPath
              : current.baseType,
          level: !isNaN(levelFromPath) ? levelFromPath : current.level,
          // Set category to ALL if not specified in the URL for town-hall bases
          category:
            categoryFromQuery ||
            (typeFromPath === 'town-hall' ? 'ALL' : undefined),
          sortBy: sortFromQuery || current.sortBy,
        }));
      }
    }
  }, [pathname, searchParams]);

  // Memoize context value to prevent unnecessary re-renders
  const contextValue = useMemo(
    () => ({
      ...filters,
      isLoading,
      updateFilters,
      resetFilters,
    }),
    [filters, isLoading, updateFilters, resetFilters]
  );

  return (
    <FilterContext.Provider value={contextValue}>
      {children}
    </FilterContext.Provider>
  );
}

// Custom hook to use the filter context
export function useFilterContext() {
  const context = useContext(FilterContext);
  if (context === undefined) {
    throw new Error('useFilterContext must be used within a FilterProvider');
  }
  return context;
}
