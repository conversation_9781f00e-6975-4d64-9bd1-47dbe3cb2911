/* Import the required fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');
@import './clash-font.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /* Apply Clash font to all heading elements */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: var(--font-clash), 'Clash', system-ui, sans-serif;
  }

  /* Supercell Headline font class */
  .font-supercell {
    font-family: var(--font-supercell), 'Supercell', system-ui, sans-serif;
  }

  /* Eye icon styling */
  .icon-eye {
    @apply text-black dark:text-white;
  }

  :root {
    --background: oklch(0.99 0.01 0);
    --destructive-foreground: oklch(1 0 0);
    --foreground: oklch(0 0 0);
    --card: oklch(1 0 0);
    --card-foreground: oklch(0 0 0);
    --popover: oklch(0.99 0 0);
    --popover-foreground: oklch(0 0 0);
    --primary: oklch(0 0 0);
    --primary-foreground: oklch(1 0 0);
    --secondary: oklch(0.94 0 0);
    --secondary-foreground: oklch(0 0 0);
    --muted: oklch(0.97 0 0);
    --muted-foreground: oklch(0.44 0 0);
    --accent: oklch(0.94 0 0);
    --accent-foreground: oklch(0 0 0);
    --destructive: oklch(0.63 0.19 23.03);
    --destructive-foreground: oklch(1 0 0);
    --border: oklch(0.92 0 0);
    --input: oklch(0.94 0 0);
    --ring: oklch(0 0 0);
    --chart-1: oklch(0.81 0.17 75.35);
    --chart-2: oklch(0.55 0.22 264.53);
    --chart-3: oklch(0.72 0 0);
    --chart-4: oklch(0.92 0 0);
    --chart-5: oklch(0.56 0 0);
    --sidebar: oklch(0.99 0 0);
    --sidebar-foreground: oklch(0 0 0);
    --sidebar-primary: oklch(0 0 0);
    --sidebar-primary-foreground: oklch(1 0 0);
    --sidebar-accent: oklch(0.94 0 0);
    --sidebar-accent-foreground: oklch(0 0 0);
    --sidebar-border: oklch(0.94 0 0);
    --sidebar-ring: oklch(0 0 0);
    --font-sans: Poppins, sans-serif;
    --font-serif: ui-serif, Georgia, Cambria, 'Times New Roman', Times, serif;
    --font-mono: JetBrains Mono, monospace;
    --radius: 0.5rem;
    --shadow-2xs: 0px 1px 2px 0px hsl(0 0% 0% / 0.09);
    --shadow-xs: 0px 1px 2px 0px hsl(0 0% 0% / 0.09);
    --shadow-sm:
      0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 1px 2px -1px hsl(0 0% 0% / 0.18);
    --shadow:
      0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 1px 2px -1px hsl(0 0% 0% / 0.18);
    --shadow-md:
      0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 2px 4px -1px hsl(0 0% 0% / 0.18);
    --shadow-lg:
      0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 4px 6px -1px hsl(0 0% 0% / 0.18);
    --shadow-xl:
      0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 8px 10px -1px hsl(0 0% 0% / 0.18);
    --shadow-2xl: 0px 1px 2px 0px hsl(0 0% 0% / 0.45);
    --tracking-normal: 0em;
    --spacing: 0.25rem;
  }

  .dark {
    --destructive-foreground: oklch(1 0 0);
    --background: oklch(0 0 0);
    --foreground: oklch(1 0 0);
    --card: oklch(0.14 0 0);
    --card-foreground: oklch(1 0 0);
    --popover: oklch(0.18 0 0);
    --popover-foreground: oklch(1 0 0);
    --primary: oklch(1 0 0);
    --primary-foreground: oklch(0 0 0);
    --secondary: oklch(0.25 0 0);
    --secondary-foreground: oklch(1 0 0);
    --muted: oklch(0.23 0 0);
    --muted-foreground: oklch(0.72 0 0);
    --accent: oklch(0.32 0 0);
    --accent-foreground: oklch(1 0 0);
    --destructive: oklch(0.69 0.2 23.91);
    --destructive-foreground: oklch(0 0 0);
    --border: oklch(0.26 0 0);
    --input: oklch(0.32 0 0);
    --ring: oklch(0.72 0 0);
    --chart-1: oklch(0.81 0.17 75.35);
    --chart-2: oklch(0.58 0.21 260.84);
    --chart-3: oklch(0.56 0 0);
    --chart-4: oklch(0.44 0 0);
    --chart-5: oklch(0.92 0 0);
    --sidebar: oklch(0.18 0 0);
    --sidebar-foreground: oklch(1 0 0);
    --sidebar-primary: oklch(1 0 0);
    --sidebar-primary-foreground: oklch(0 0 0);
    --sidebar-accent: oklch(0.32 0 0);
    --sidebar-accent-foreground: oklch(1 0 0);
    --sidebar-border: oklch(0.32 0 0);
    --sidebar-ring: oklch(0.72 0 0);
    --font-sans: Poppins, sans-serif;
    --font-serif: ui-serif, Georgia, Cambria, 'Times New Roman', Times, serif;
    --font-mono: JetBrains Mono, monospace;
    --radius: 0.5rem;
    --shadow-2xs: 0px 1px 2px 0px hsl(0 0% 0% / 0.09);
    --shadow-xs: 0px 1px 2px 0px hsl(0 0% 0% / 0.09);
    --shadow-sm:
      0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 1px 2px -1px hsl(0 0% 0% / 0.18);
    --shadow:
      0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 1px 2px -1px hsl(0 0% 0% / 0.18);
    --shadow-md:
      0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 2px 4px -1px hsl(0 0% 0% / 0.18);
    --shadow-lg:
      0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 4px 6px -1px hsl(0 0% 0% / 0.18);
    --shadow-xl:
      0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 8px 10px -1px hsl(0 0% 0% / 0.18);
    --shadow-2xl: 0px 1px 2px 0px hsl(0 0% 0% / 0.45);
  }
}

@theme inline {
  --color-destructive-foreground: oklch(1 0 0);
  --color-color-destructive-foreground: var(----color-destructive-foreground);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  /* Border radius variables */
  --radius: 0.5rem;
  --radius-sm: calc(0.5rem - 4px);
  --radius-md: calc(0.5rem - 2px);
  --radius-lg: 0.5rem;
  --radius-xl: calc(0.5rem + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-sans);
  }
}

@layer components {
  /* Card styles similar to the dashboard cards */
  .stat-card {
    @apply rounded-lg border bg-card p-6 shadow-sm;
  }

  .stat-card-title {
    @apply text-sm font-medium text-muted-foreground;
  }

  .stat-card-value {
    @apply mt-2 text-3xl font-bold;
  }

  .stat-card-description {
    @apply mt-2 text-xs text-muted-foreground;
  }

  .stat-card-badge {
    @apply inline-flex items-center rounded-md px-2 py-1 text-xs font-medium text-secondary-foreground;
    background-color: var(--secondary);
    opacity: 0.3;
  }

  /* Navigation styles similar to the sidebar */
  .sidebar-nav {
    @apply flex flex-col space-y-1;
  }

  .sidebar-nav-item {
    @apply flex items-center rounded-md px-3 py-2 text-sm font-medium transition-colors;
  }

  .sidebar-nav-item:hover {
    @apply bg-sidebar-accent text-sidebar-accent-foreground;
  }

  .sidebar-nav-item.active {
    @apply bg-sidebar-primary text-sidebar-primary-foreground;
  }

  /* Button styles similar to the UI */
  .btn-primary {
    @apply inline-flex h-9 items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow transition-colors;
  }
  .btn-primary:hover {
    opacity: 0.9;
  }

  .btn-secondary {
    @apply inline-flex h-9 items-center justify-center rounded-md bg-secondary px-4 py-2 text-sm font-medium text-secondary-foreground shadow-sm transition-colors;
  }
  .btn-secondary:hover {
    opacity: 0.8;
  }

  .btn-outline {
    @apply inline-flex h-9 items-center justify-center rounded-md border border-input bg-background px-4 py-2 text-sm font-medium shadow-sm transition-colors;
  }
  .btn-outline:hover {
    @apply bg-accent text-accent-foreground;
  }

  /* Table styles similar to the shown table */
  .table-container {
    @apply w-full overflow-auto rounded-md border;
  }

  .table {
    @apply w-full text-sm;
  }

  .table-header {
    background-color: var(--muted);
    opacity: 0.5;
  }

  .table-row {
    @apply border-b transition-colors;
  }
  .table-row:hover {
    background-color: var(--muted);
    opacity: 0.3;
  }

  .table-cell {
    @apply p-4 align-middle font-medium;
  }

  /* Pricing card styles */
  .pricing-card {
    @apply flex flex-col rounded-lg border bg-card p-6 shadow-sm;
  }

  .pricing-title {
    @apply text-lg font-semibold;
  }

  .pricing-description {
    @apply text-sm text-muted-foreground;
  }

  .pricing-amount {
    @apply mt-4 text-4xl font-bold;
  }

  .pricing-period {
    @apply text-sm font-normal text-muted-foreground;
  }

  /* Form input styles */
  .form-input {
    @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm shadow-sm transition-colors;
  }
  .form-input:focus-visible {
    @apply outline-none ring-2 ring-ring;
  }
  .form-input:disabled {
    @apply cursor-not-allowed opacity-50;
  }

  /* Tag styles for mail categories */
  .tag {
    @apply inline-flex items-center rounded-md px-2 py-1 text-xs font-medium;
  }

  .tag-blue {
    @apply bg-blue-100 text-blue-800;
  }
  .dark .tag-blue {
    @apply text-blue-300;
    background-color: rgb(30 58 138 / 0.3); /* blue-900 with 0.3 opacity */
  }

  .tag-green {
    @apply bg-green-100 text-green-800;
  }
  .dark .tag-green {
    @apply text-green-300;
    background-color: rgb(20 83 45 / 0.3); /* green-900 with 0.3 opacity */
  }

  .tag-red {
    @apply bg-red-100 text-red-800;
  }
  .dark .tag-red {
    @apply text-red-300;
    background-color: rgb(127 29 29 / 0.3); /* red-900 with 0.3 opacity */
  }

  .tag-yellow {
    @apply bg-yellow-100 text-yellow-800;
  }
  .dark .tag-yellow {
    @apply text-yellow-300;
    background-color: rgb(113 63 18 / 0.3); /* yellow-900 with 0.3 opacity */
  }

  .tag-purple {
    @apply bg-purple-100 text-purple-800;
  }
  .dark .tag-purple {
    @apply text-purple-300;
    background-color: rgb(88 28 135 / 0.3); /* purple-900 with 0.3 opacity */
  }

  /* Tabs like in the UI */
  .tabs {
    @apply inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground;
  }

  .tabs-item {
    @apply inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all;
  }
  .tabs-item:focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2;
  }
  .tabs-item:disabled {
    @apply pointer-events-none opacity-50;
  }
  .tabs-item[data-state='active'] {
    @apply bg-background text-foreground shadow;
  }

  /* Data display styles */
  .data-card {
    @apply grid gap-1;
  }

  .data-card-label {
    @apply text-xs font-medium text-muted-foreground;
  }

  .data-card-value {
    @apply text-xl font-bold;
  }

  .data-card-delta {
    @apply flex items-center text-xs font-medium;
  }

  .data-card-delta-positive {
    @apply text-emerald-500;
  }

  .data-card-delta-negative {
    @apply text-rose-500;
  }

  /* Layout styles */
  .layout {
    @apply mx-auto max-w-7xl px-4 sm:px-6 lg:px-8;
  }

  .header {
    @apply border-b;
  }

  .header-inner {
    @apply flex h-16 items-center justify-between;
  }

  .section {
    @apply py-8;
  }

  .section-title {
    @apply text-2xl font-bold tracking-tight;
  }

  /* Search input like in mail app */
  .search-input {
    @apply flex h-9 w-full items-center rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors;
  }
  .search-input:focus-within {
    @apply ring-1 ring-ring;
  }

  .search-input input {
    @apply flex-1 bg-transparent outline-none;
  }
  .search-input input::placeholder {
    @apply text-muted-foreground;
  }
}

/* Area charts styling like the one in dashboard */
.area-chart path {
  @apply stroke-chart-2 stroke-2;
  fill: var(--chart-2);
  fill-opacity: 0.2;
}

.area-chart-secondary path {
  @apply stroke-chart-1 stroke-2;
  fill: var(--chart-1);
  fill-opacity: 0.2;
}

/* Stats Card Component */
.stats-card {
  @apply rounded-lg border bg-card p-6 shadow-sm;
}

/* Dashboard Components */
.dashboard {
  @apply flex flex-col gap-8;
}

.dashboard-header {
  @apply flex flex-col items-start justify-between gap-4 md:flex-row md:items-center;
}

.dashboard-title {
  @apply text-2xl font-bold tracking-tight;
}

.dashboard-description {
  @apply text-sm text-muted-foreground;
}

.dashboard-actions {
  @apply flex items-center gap-2;
}

.dashboard-section {
  @apply grid gap-4;
}

.card {
  @apply rounded-lg border bg-card shadow-sm;
}

.card-content {
  @apply flex flex-col p-6;
}

.card-header {
  @apply mb-4 space-y-1;
}

.card-title {
  @apply text-lg font-medium;
}

.card-description {
  @apply text-sm text-muted-foreground;
}

.card-body {
  @apply flex-1;
}

.card-footer {
  @apply mt-6 flex items-center justify-between;
}

html {
  scroll-behavior: smooth;
  scroll-padding-top: 4rem;
}

.baseLevel {
  position: absolute;
  right: -2px;
  top: -8px;
  color: #ffb519;
  font-weight: 900;
  font-size: 70px;
  line-height: 1;
}

.grit-mask {
  -webkit-mask-image: url('/images/grit-mask.png');
  mask-image: url('/images/grit-mask.png');
  -webkit-mask-repeat: repeat;
  mask-repeat: repeat;
  -webkit-mask-size: 250px;
  mask-size: 250px;
}

.heading {
  -webkit-text-strokewidth: 0.1em;
  letter-spacing: -0.03em;
  paint-order: stroke fill;
  text-shadow:
    0 0.1em 0 #000,
    -0.03em 0.1em 0 #000,
    0.03em 0.1em 0 #000;
  -webkit-text-stroke-color: #000;
  text-shadow:
    0 0.05em 0 #000,
    -0.015em 0.07em 0 #000,
    0.015em 0.07em 0 #000;
  -webkit-text-strokewidth: 0.075em;
}

.eye {
  animation: blink 2s infinite;
}

.icon-eye .eye {
  animation: blink 0.8s infinite;
}

/** Blink animation */
@keyframes blink {
  0%,
  90% {
    d: path(
      'M10.0004 3.33325C13.5754 3.33325 16.5529 5.35742 18.8879 9.29492L19.0712 9.61075L19.1087 9.69409L19.1337 9.76325L19.1454 9.80909L19.157 9.87742L19.1662 9.96075V10.0524L19.1545 10.1449C19.1491 10.1759 19.1418 10.2065 19.1329 10.2366L19.1004 10.3266L19.0704 10.3891L19.057 10.4141C16.7537 14.4441 13.807 16.5641 10.2612 16.6633L10.0004 16.6666C6.33705 16.6666 3.30288 14.5424 0.943716 10.4133C0.871807 10.2874 0.833984 10.1449 0.833984 9.99992C0.833984 9.85494 0.871807 9.71247 0.943716 9.58659C3.30288 5.45742 6.33705 3.33325 10.0004 3.33325ZM10.0004 7.49992C9.33734 7.49992 8.70146 7.76331 8.23261 8.23215C7.76377 8.70099 7.50038 9.33688 7.50038 9.99992C7.50038 10.663 7.76377 11.2988 8.23261 11.7677C8.70146 12.2365 9.33734 12.4999 10.0004 12.4999C10.6634 12.4999 11.2993 12.2365 11.7681 11.7677C12.237 11.2988 12.5004 10.663 12.5004 9.99992C12.5004 9.33688 12.237 8.70099 11.7681 8.23215C11.2993 7.76331 10.6634 7.49992 10.0004 7.49992Z'
    );
  }

  91%,
  100% {
    d: path('M2 10C5 7 15 7 18 10C15 13 5 13 2 10Z');
  }
}
