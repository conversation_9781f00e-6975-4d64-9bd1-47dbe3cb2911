import { v2 as cloudinary } from 'cloudinary';

// Parse CLOUDINARY_URL if it exists
const parseCloudinaryUrl = () => {
  const url = process.env.CLOUDINARY_URL;
  if (!url) return null;

  try {
    // Format: cloudinary://api_key:api_secret@cloud_name
    const match = url.match(/cloudinary:\/\/([^:]+):([^@]+)@(.+)/);
    if (match) {
      return {
        cloud_name: match[3],
        api_key: match[1],
        api_secret: match[2],
        secure: true,
      };
    }
  } catch (e) {
    console.error('Failed to parse CLOUDINARY_URL:', e);
  }
  return null;
};

// Configure Cloudinary with appropriate credentials
const credentials = parseCloudinaryUrl();

if (credentials) {
  console.log('Configuring Cloudinary with parsed CLOUDINARY_URL');
  cloudinary.config(credentials);
} else if (
  process.env.CLOUDINARY_CLOUD_NAME &&
  process.env.CLOUDINARY_API_KEY &&
  process.env.CLOUDINARY_API_SECRET
) {
  console.log('Configuring Cloudinary with individual environment variables');
  cloudinary.config({
    cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
    api_key: process.env.CLOUDINARY_API_KEY,
    api_secret: process.env.CLOUDINARY_API_SECRET,
    secure: true,
  });
} else {
  // Development fallback with environment-specific values
  console.log('Configuring Cloudinary with development fallback');
  cloudinary.config({
    cloud_name: 'denmiluax',
    api_key: process.env.CLOUDINARY_API_KEY || '156894536836384',
    api_secret:
      process.env.CLOUDINARY_API_SECRET || 'oQ5bIgyqhgIl6Gh1y-6PBSBkwn4',
    secure: true,
  });
}

export default cloudinary;
