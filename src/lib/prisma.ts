import { Prisma, PrismaClient } from '@prisma/client';

// Custom options for the Prisma client
const prismaClientOptions: Prisma.PrismaClientOptions = {
  log: ['error'],
};

// Create a singleton factory to prevent multiple clients in development
const prismaClientSingleton = () => {
  return new PrismaClient(prismaClientOptions);
};

declare global {
  // eslint-disable-next-line no-var
  var prisma: ReturnType<typeof prismaClientSingleton> | undefined;
}

// Use existing client if available (in development) or create new one
const prisma = global.prisma || prismaClientSingleton();

// Store the client in the global object in development to prevent multiple instances
if (process.env.NODE_ENV === 'development') global.prisma = prisma;

export default prisma;
