import { unstable_cache } from 'next/cache';

import prisma from '@/lib/prisma';

export const getBuilderHallStats = unstable_cache(
  async () => {
    // Builder Hall levels 3-10
    const levels = Array.from({ length: 8 }, (_, i) => 10 - i); // [10, 9, ..., 3]
    const stats = await Promise.all(
      levels.map(async (level) => {
        const [count, views] = await Promise.all([
          prisma.base.count({ where: { type: 'BUILDER_HALL', level } }),
          prisma.base.aggregate({
            where: { type: 'BUILDER_HALL', level },
            _sum: { views: true },
          }),
        ]);
        return {
          bhLevel: level,
          count,
          views: views._sum.views || 0,
        };
      })
    );
    return stats;
  },
  [],
  { revalidate: 5 } // Shorter revalidation time to reflect changes sooner
);
