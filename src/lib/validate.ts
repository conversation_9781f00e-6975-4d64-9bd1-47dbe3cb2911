// Use a string union for BaseType due to Prisma/TS bug
export type BaseType = 'TOWN_HALL' | 'BUILDER_HALL';

import { z } from 'zod';

export const isValidUUID = (id: string) => {
  return /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(
    id
  );
};

export const isValidBaseType = (type: unknown): type is BaseType => {
  return type === 'TOWN_HALL' || type === 'BUILDER_HALL';
};

export const baseVisitSchema = z.object({
  id: z.string().uuid(),
});

export const categoryVisitSchema = z.object({
  type: z.enum(['TOWN_HALL', 'BUILDER_HALL']),
  level: z.number().int().min(1),
});
