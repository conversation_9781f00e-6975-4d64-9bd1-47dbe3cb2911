import { BaseCategory } from '@prisma/client';

import prisma from '@/lib/prisma';

export async function getBaseTypes() {
  // Optionally, you could fetch distinct types from DB
  // For now, return static types
  return ['War', 'Farming', 'Trophy', 'Hybrid'];
}

export async function getBasesByTH(level: number, category?: BaseCategory) {
  return prisma.base.findMany({
    where: { type: 'TOWN_HALL', level, ...(category ? { category } : {}) },
    orderBy: { createdAt: 'desc' },
  });
}
