import { unstable_cache } from 'next/cache';

import prisma from '@/lib/prisma';

export const getTownHallStats = unstable_cache(
  async () => {
    // Town Hall levels 1-17
    const levels = Array.from({ length: 17 }, (_, i) => 17 - i); // [17, 16, ..., 1]
    const stats = await Promise.all(
      levels.map(async (level) => {
        const [count, views] = await Promise.all([
          prisma.base.count({ where: { type: 'TOWN_HALL', level } }),
          prisma.base.aggregate({
            where: { type: 'TOWN_HALL', level },
            _sum: { views: true },
          }),
        ]);
        return {
          thLevel: level,
          count,
          views: views._sum.views || 0,
        };
      })
    );
    return stats;
  },
  [],
  { revalidate: 5 } // Shorter revalidation time to reflect changes sooner
);
