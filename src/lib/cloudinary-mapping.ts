import cloudinary from '@/lib/cloudinary';

/**
 * In-memory cache for Cloudinary image mappings, per folder.
 * Resets on serverless cold start.
 */
const imageMapCache: Record<string, Record<string, string>> = {};

/**
 * Fetches all images from a Cloudinary folder and returns a mapping from prefix (e.g. bh9, th17) to secure_url.
 * Uses in-memory cache for performance (per serverless invocation).
 * @param folder The Cloudinary folder to search (e.g. 'builderhalls' or 'townhalls')
 */
export async function getCloudinaryImageMap(
  folder: string
): Promise<Record<string, string>> {
  if (imageMapCache[folder]) {
    return imageMapCache[folder];
  }
  const result: Record<string, string> = {};
  let nextCursor: string | undefined = undefined;
  try {
    do {
      const response = await cloudinary.search
        .expression(`folder:${folder}`)
        .max_results(100)
        .next_cursor(nextCursor)
        .execute();
      for (const asset of response.resources) {
        // asset.public_id: e.g. 'builderhalls/bh9' or 'townhalls/th17'
        // asset.secure_url: full image URL
        // Extract the filename (without folder and extension)
        const parts = asset.public_id.split('/');
        const filename = parts[parts.length - 1]; // e.g. 'th9' or 'bh9'
        result[filename] = asset.secure_url;
      }
      nextCursor = response.next_cursor;
    } while (nextCursor);
    imageMapCache[folder] = result;
    return result;
  } catch (error) {
    // Log error for debugging, return empty mapping
    console.error('Cloudinary mapping error:', error);
    return {};
  }
}
