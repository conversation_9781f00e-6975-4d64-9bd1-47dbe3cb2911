import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export const cn = (...inputs: ClassValue[]) => twMerge(clsx(inputs));

/**
 * Formats a number to a more readable format:
 * - Numbers < 1,000 display as-is (e.g., "999")
 * - Numbers 1,000-9,999 display with one decimal (e.g., "1.5K")
 * - Numbers 10,000-999,999 display without decimal (e.g., "15K")
 * - Numbers >= 1,000,000 display with one decimal and + (e.g., "1.2M+")
 */
export function formatViewCount(count: number): string {
  if (count >= 1_000_000) {
    // For 1M+ we show one decimal point and add the '+' suffix
    return `${(count / 1_000_000).toFixed(1).replace(/\.0$/, '')}M+`;
  } else if (count >= 10_000) {
    // For numbers over 10K, we don't need the decimal point
    return `${Math.floor(count / 1_000)}K`;
  } else if (count >= 1_000) {
    // For 1K-10K we show one decimal point
    return `${(count / 1_000).toFixed(1).replace(/\.0$/, '')}K`;
  } else {
    // For less than 1000, just show the exact number
    return count.toLocaleString();
  }
}

/**
 * Format a date string consistently across server and client
 * using a fixed locale (en-US) and format (MM/DD/YYYY)
 */
export function formatDate(dateString: string | Date): string {
  const date =
    typeof dateString === 'string' ? new Date(dateString) : dateString;

  // Use en-US locale to ensure consistency between server and client
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
  });
}

/**
 * Creates an AbortSignal with a timeout and proper error handling
 * @param timeoutMs Timeout in milliseconds (default: 8000ms)
 * @returns AbortSignal that will abort after the specified timeout
 */
export function createTimeoutSignal(timeoutMs = 8000): AbortSignal {
  try {
    return AbortSignal.timeout(timeoutMs);
  } catch {
    // Fallback for browsers that don't support AbortSignal.timeout
    const controller = new AbortController();
    setTimeout(
      () => controller.abort(new DOMException('Timeout', 'TimeoutError')),
      timeoutMs
    );
    return controller.signal;
  }
}
