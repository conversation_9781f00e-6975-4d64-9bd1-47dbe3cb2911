import { BaseCategory } from '@prisma/client';

import prisma from '@/lib/prisma';

export async function getBuilderBasesByLevel(
  level: number,
  category?: BaseCategory
) {
  return prisma.base.findMany({
    where: { type: 'BUILDER_HALL', level, ...(category ? { category } : {}) },
    orderBy: { createdAt: 'desc' },
  });
}

export async function getBuilderBaseTypes() {
  // Optionally, fetch distinct types from DB
  return ['Trophy', 'Versus', 'Hybrid'];
}
