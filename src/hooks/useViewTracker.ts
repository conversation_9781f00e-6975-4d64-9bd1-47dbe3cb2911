'use client';
import { useEffect, useState } from 'react';

// Cache to store in-memory tracked items across component instances with timestamps
interface ViewRecord {
  timestamp: number;
}
let globalViewedCache: Record<string, ViewRecord> = {};

// Time in milliseconds before a view can be counted again (5 minutes by default)
// This prevents excessive API calls when repeatedly viewing the same content
const VIEW_EXPIRATION_TIME = 5 * 60 * 1000; // 5 minutes

/**
 * Hook for tracking views of bases and categories
 *
 * This hook ensures that a view is only counted once per browser session
 * within a specific time window (5 minutes by default)
 */
export function useViewTracker() {
  // We only need to use setViewedItems for sync purposes, not the actual state
  const [, setViewedItems] =
    useState<Record<string, ViewRecord>>(globalViewedCache);
  const [isInitialized, setIsInitialized] = useState(false);

  // Load previously viewed items from sessionStorage on mount
  useEffect(() => {
    // Only run in browser environment and once
    if (typeof window === 'undefined' || isInitialized) return;

    try {
      const viewedBasesString = sessionStorage.getItem('viewed_items');
      const viewed = viewedBasesString ? JSON.parse(viewedBasesString) : {};

      // Update both local state and global cache
      globalViewedCache = { ...globalViewedCache, ...viewed };
      setViewedItems(globalViewedCache);
      setIsInitialized(true);
    } catch (err) {
      // If there's an error reading from sessionStorage, create a new empty object
      console.error('Error reading from sessionStorage', err);
      setIsInitialized(true);
    }
  }, [isInitialized]);

  /**
   * Check if an item has been viewed recently (within the expiration time)
   * This checks both the local state and directly from sessionStorage
   */
  const hasBeenViewed = (key: string): boolean => {
    if (typeof window === 'undefined') return false;

    const now = Date.now();

    // First check in memory for performance
    if (
      globalViewedCache[key] &&
      now - globalViewedCache[key].timestamp < VIEW_EXPIRATION_TIME
    ) {
      return true;
    }

    // If not in memory, check sessionStorage directly
    try {
      const viewedBasesString = sessionStorage.getItem('viewed_items');
      if (!viewedBasesString) return false;

      const viewed = JSON.parse(viewedBasesString);
      const now = Date.now();

      // Check if the item exists and if it's viewed within the expiration time
      return Boolean(
        viewed[key] && now - viewed[key].timestamp < VIEW_EXPIRATION_TIME
      );
    } catch {
      return false;
    }
  };

  /**
   * Mark an item as viewed
   * @returns true if this is the first time viewing or if the previous view has expired
   */
  const markAsViewed = (key: string): boolean => {
    if (typeof window === 'undefined') return false;

    // Check directly in global cache and sessionStorage
    if (hasBeenViewed(key)) {
      return false; // Already viewed recently
    }

    try {
      const now = Date.now();
      // Update global cache first with timestamp
      globalViewedCache = {
        ...globalViewedCache,
        [key]: { timestamp: now },
      };

      // Then update local state
      setViewedItems(globalViewedCache);

      // Finally update sessionStorage
      sessionStorage.setItem('viewed_items', JSON.stringify(globalViewedCache));
      return true;
    } catch (err) {
      console.error('Error writing to sessionStorage', err);
      return false;
    }
  };

  return { hasBeenViewed, markAsViewed };
}
