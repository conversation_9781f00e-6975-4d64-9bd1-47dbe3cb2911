'use client';

import { useEffect } from 'react';

export function SuppressHydrationWarnings() {
  useEffect(() => {
    // This suppresses the hydration warning for 'cz-shortcut-listen' attribute
    // which is typically added by browser extensions or DevTools
    const originalConsoleError = console.error;
    console.error = (...args) => {
      if (
        typeof args[0] === 'string' &&
        args[0].includes('Extra attributes from the server') &&
        args[0].includes('cz-shortcut-listen')
      ) {
        // Suppress the specific warning
        return;
      }
      originalConsoleError(...args);
    };

    // Restore original console.error on unmount
    return () => {
      console.error = originalConsoleError;
    };
  }, []);

  return null; // This component doesn't render anything
}
