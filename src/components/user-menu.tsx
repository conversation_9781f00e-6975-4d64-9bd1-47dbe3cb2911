'use client';
import { useEffect, useRef, useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { signOut, useSession } from 'next-auth/react';

export default function UserMenu() {
  const { data: session } = useSession();
  const [open, setOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setOpen(false);
      }
    }
    if (open) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [open]);

  if (!session?.user?.image) return null;

  return (
    <div className="relative" ref={menuRef}>
      <button
        className="focus:outline-none"
        onClick={() => setOpen((v) => !v)}
        aria-label="User menu"
      >
        <Image
          src={session.user.image}
          alt="Profile"
          width={40}
          height={40}
          className="size-10 rounded-full border-2 border-white shadow-lg transition hover:ring-2 hover:ring-blue-500"
        />
      </button>
      {open && (
        <div className="absolute right-0 z-50 mt-2 w-40 rounded-lg bg-white py-2 shadow-xl">
          {session.user.email === '<EMAIL>' && (
            <Link
              href="/admin"
              className="block w-full px-4 py-2 text-left text-gray-700 hover:bg-gray-100"
            >
              Admin Panel
            </Link>
          )}
          <button
            className="block w-full px-4 py-2 text-left text-gray-700 hover:bg-gray-100"
            onClick={() => signOut()}
          >
            Sign out
          </button>
        </div>
      )}
    </div>
  );
}
