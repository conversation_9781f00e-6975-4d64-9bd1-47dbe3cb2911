'use client';

import { useState } from 'react';
import { ChevronRightIcon } from '@radix-ui/react-icons';
import Image from 'next/image';

import {
  Button,
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
  Label,
  RadioGroup,
  RadioGroupItem,
} from '@/components/ui';
import {
  BaseCategory as FilterBaseCategory,
  BaseType as FilterBaseType,
  SortOption as FilterSortOption,
  useFilterContext,
} from '@/context/FilterContext';
import { cn } from '@/lib/utils';

// Define local types for backward compatibility
type BaseCategory = 'ALL' | 'WAR' | 'TROPHY' | 'FARMING' | 'HYBRID' | 'OTHER';

interface BaseSidebarProps {
  initialType?: 'town-hall' | 'builder-hall';
  initialLevel?: number;
  initialCategory?: string;
  initialSort?: string;
  onFilterChange?: (filters: {
    type?: string;
    level?: number;
    category?: string;
    sort?: string;
  }) => void;
}

const townHallLevels = [17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4];
const builderHallLevels = [10, 9, 8, 7, 6, 5, 4, 3];

const baseCategories: BaseCategory[] = [
  'ALL',
  'WAR',
  'TROPHY',
  'FARMING',
  'HYBRID',
  'OTHER',
];

export function BaseSidebar({
  initialType = 'town-hall',
  initialLevel = 17,
  initialCategory = 'ALL',
  initialSort = 'newest',
  onFilterChange,
}: BaseSidebarProps) {
  // Use our context instead of local state
  // With fallbacks in case context is not available
  let baseType = initialType;
  let level = initialLevel;
  let category = initialCategory as FilterBaseCategory;
  let sortBy = initialSort as FilterSortOption;
  let isLoading = false;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let updateFilters: (updates: any) => void = () => {};
  let resetFilters: () => void = () => {};

  try {
    const filterContext = useFilterContext();
    baseType = filterContext.baseType;
    level = filterContext.level;
    category = filterContext.category || 'ALL';
    sortBy = filterContext.sortBy;
    isLoading = filterContext.isLoading;
    updateFilters = filterContext.updateFilters;
    resetFilters = filterContext.resetFilters;
  } catch (error) {
    console.error('Failed to access FilterContext:', error);
  }

  // Section open state for collapsibles (UI only state)
  const [isLevelOpen, setIsLevelOpen] = useState(true);
  const [isFiltersOpen, setIsFiltersOpen] = useState(true);
  const [isSortOpen, setIsSortOpen] = useState(true);

  // Get current levels based on baseType
  const currentLevels =
    baseType === 'town-hall' ? townHallLevels : builderHallLevels;

  // Handler for notifying parent component of changes (backward compatibility)
  const notifyParentOfChanges = () => {
    if (onFilterChange) {
      onFilterChange({
        type: baseType,
        level,
        category: baseType === 'town-hall' ? category : undefined,
        sort: sortBy,
      });
    }
  };

  return (
    <div className="bg-secondary w-full rounded-lg border p-8 shadow-sm">
      {/* Base Type Selection */}
      <div className="mb-6">
        <h3 className="font-supercell mb-3 flex items-center justify-between text-xl font-bold">
          {baseType === 'town-hall' ? 'Town Hall Bases' : 'Builder Hall Bases'}
        </h3>

        <RadioGroup
          value={baseType}
          disabled={isLoading}
          onValueChange={(value: FilterBaseType) => {
            updateFilters({ baseType: value });
            notifyParentOfChanges();
          }}
          className="flex gap-3"
        >
          <div className="flex flex-1 items-center ">
            <RadioGroupItem
              value="town-hall"
              id="town-hall"
              className="sr-only" // Visually hidden but accessible
            />
            <Label
              htmlFor="town-hall"
              className={`inline-flex h-10 w-full cursor-pointer items-center justify-center rounded-md px-4 text-sm font-medium transition-colors ${
                baseType === 'town-hall'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-[#464649] dark:text-gray-200 dark:hover:bg-gray-600'
              }`}
            >
              Town Hall Bases
            </Label>
          </div>
          <div className="flex flex-1 items-center">
            <RadioGroupItem
              value="builder-hall"
              id="builder-hall"
              className="sr-only" // Visually hidden but accessible
            />
            <Label
              htmlFor="builder-hall"
              className={`inline-flex h-10 w-full cursor-pointer items-center justify-center rounded-md px-4 text-sm font-medium transition-colors ${
                baseType === 'builder-hall'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-[#464649] dark:text-gray-200 dark:hover:bg-gray-600'
              }`}
            >
              Builder Hall Bases
            </Label>
          </div>
        </RadioGroup>
      </div>

      {/* Hall Level Selection */}
      <Collapsible
        open={isLevelOpen}
        onOpenChange={setIsLevelOpen}
        className="mb-2"
      >
        <div className="mb-2 flex items-center justify-between">
          <CollapsibleTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="dark:bg-accent hover:bg-accent-foreground bg-accent-foreground !h-12 w-full justify-between px-3 py-2 text-white hover:text-white"
            >
              <h3 className="font-supercell text-xl font-bold">
                {baseType === 'town-hall'
                  ? 'Town Hall Level'
                  : 'Builder Hall Level'}
              </h3>
              <ChevronRightIcon
                className={`size-5 transition-transform ${
                  isLevelOpen ? 'rotate-90' : ''
                }`}
              />
            </Button>
          </CollapsibleTrigger>
        </div>

        <CollapsibleContent>
          <div className="grid grid-cols-3 gap-3">
            {currentLevels.map((lvl) => (
              <button
                key={lvl}
                disabled={isLoading}
                className={`relative flex flex-col items-center gap-0 rounded-lg p-3 transition-colors
                  ${level === lvl ? 'bg-blue-600 text-white' : 'bg-[#464649] hover:bg-[#71717b]'}
                  ${isLoading ? 'cursor-not-allowed opacity-70' : ''}`}
                onClick={() => {
                  // Skip if already selected or loading
                  if (isLoading || level === lvl) return;
                  updateFilters({ level: lvl });
                  notifyParentOfChanges();
                }}
              >
                {isLoading && level === lvl ? (
                  <div className="absolute inset-0 flex items-center justify-center rounded-lg bg-blue-600/80">
                    <div className="size-6 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                  </div>
                ) : null}
                <Image
                  src={`/images/${
                    baseType === 'town-hall' ? 'TownHalls' : 'BuilderHalls'
                  }/${baseType === 'town-hall' ? 'th' : 'bh'}${lvl}.png`}
                  alt={`${baseType === 'town-hall' ? 'TH' : 'BH'}${lvl}`}
                  width={40}
                  height={40}
                  className="mb-1"
                />
                <span className="font-mono text-sm font-medium text-white">
                  {baseType === 'town-hall' ? 'TH' : 'BH'}
                  {lvl}
                </span>
              </button>
            ))}
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Category Filter (Only for Town Hall) */}
      {baseType === 'town-hall' && (
        <Collapsible
          open={isFiltersOpen}
          onOpenChange={setIsFiltersOpen}
          className="mb-2"
        >
          <div className="mb-2 flex items-center justify-between">
            <CollapsibleTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="dark:bg-accent hover:bg-accent-foreground bg-accent-foreground !h-12 w-full justify-between px-3 py-2 text-white hover:text-white"
              >
                <h3 className="font-supercell text-xl font-bold">Filters</h3>
                <ChevronRightIcon
                  className={`size-5 transition-transform ${
                    isFiltersOpen ? 'rotate-90' : ''
                  }`}
                />
              </Button>
            </CollapsibleTrigger>
          </div>

          <CollapsibleContent>
            <RadioGroup
              value={category}
              disabled={isLoading}
              onValueChange={(value) => {
                updateFilters({ category: value as FilterBaseCategory });
                notifyParentOfChanges();
              }}
              className="flex flex-col"
            >
              {baseCategories.map((cat) => (
                <div
                  key={cat}
                  className={cn(
                    'flex items-center rounded-md p-3',
                    cat === category
                      ? 'bg-blue-600'
                      : 'hover:bg-gray-200 dark:hover:bg-gray-700'
                  )}
                >
                  <RadioGroupItem value={cat} id={`category-${cat}`} />
                  <Label
                    htmlFor={`category-${cat}`}
                    className={cn(
                      'w-full cursor-pointer pl-2',
                      cat === category && 'text-white'
                    )}
                  >
                    {cat === 'ALL'
                      ? 'All Bases'
                      : cat === 'OTHER'
                        ? 'Other Base'
                        : `${cat.charAt(0) + cat.slice(1).toLowerCase()} Base`}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </CollapsibleContent>
        </Collapsible>
      )}

      {/* Sort Options */}
      <Collapsible
        open={isSortOpen}
        onOpenChange={setIsSortOpen}
        className="mb-6"
      >
        <div className="mb-2 flex items-center justify-between">
          <CollapsibleTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="dark:bg-accent hover:bg-accent-foreground bg-accent-foreground !h-12 w-full justify-between px-3 py-2 text-white hover:text-white"
            >
              <h3 className="font-supercell text-xl font-bold">Sort By</h3>

              <ChevronRightIcon
                className={`size-5 transition-transform ${
                  isSortOpen ? 'rotate-90' : ''
                }`}
              />
            </Button>
          </CollapsibleTrigger>
        </div>

        <CollapsibleContent>
          <RadioGroup
            value={sortBy}
            disabled={isLoading}
            onValueChange={(value) => {
              updateFilters({ sortBy: value as FilterSortOption });
              notifyParentOfChanges();
            }}
            className="flex flex-col"
          >
            <div
              className={cn(
                'flex items-center rounded-md p-3',
                sortBy === 'newest'
                  ? 'bg-blue-600'
                  : 'hover:bg-gray-200 dark:hover:bg-gray-700'
              )}
            >
              <RadioGroupItem value="newest" id="sort-newest" />
              <Label
                htmlFor="sort-newest"
                className={cn(
                  'w-full cursor-pointer pl-2',
                  sortBy === 'newest' && 'text-white'
                )}
              >
                Newest
              </Label>
            </div>
            <div
              className={cn(
                'flex items-center rounded-md p-3',
                sortBy === 'oldest'
                  ? 'bg-blue-600'
                  : 'hover:bg-gray-200 dark:hover:bg-gray-700'
              )}
            >
              <RadioGroupItem value="oldest" id="sort-oldest" />
              <Label
                htmlFor="sort-oldest"
                className={cn(
                  'w-full cursor-pointer pl-2',
                  sortBy === 'oldest' && 'text-white'
                )}
              >
                Oldest
              </Label>
            </div>
            <div
              className={cn(
                'flex items-center rounded-md p-3',
                sortBy === 'popular'
                  ? 'bg-blue-600'
                  : 'hover:bg-gray-200 dark:hover:bg-gray-700'
              )}
            >
              <RadioGroupItem value="popular" id="sort-popular" />
              <Label
                htmlFor="sort-popular"
                className={cn(
                  'w-full cursor-pointer pl-2',
                  sortBy === 'popular' && 'text-white'
                )}
              >
                Most Popular
              </Label>
            </div>
          </RadioGroup>
        </CollapsibleContent>
      </Collapsible>

      {/* Reset Button */}
      <Button
        variant="destructive"
        className="relative w-full"
        disabled={isLoading}
        onClick={() => {
          resetFilters();
          notifyParentOfChanges();
        }}
      >
        {isLoading ? (
          <div className="absolute inset-0 flex items-center justify-center rounded-md bg-red-600/90">
            <div className="size-5 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
          </div>
        ) : null}
        Reset the filter
      </Button>
    </div>
  );
}
