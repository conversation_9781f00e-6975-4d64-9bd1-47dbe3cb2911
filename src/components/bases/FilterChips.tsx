'use client';

import { X } from 'lucide-react';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';

interface FilterChipsProps {
  type?: 'town-hall' | 'builder-hall';
  level?: number;
  category?: string;
  sort?: string;
}

export function FilterChips({ type, level, category, sort }: FilterChipsProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const removeFilter = (filterType: 'level' | 'category' | 'sort') => {
    const params = new URLSearchParams(searchParams.toString());
    params.delete(filterType);

    // Set defaults if needed
    if (filterType === 'level' && type === 'town-hall') {
      params.set('level', '17'); // Default Town Hall level
    } else if (filterType === 'level' && type === 'builder-hall') {
      params.set('level', '10'); // Default Builder Hall level
    } else if (filterType === 'sort') {
      params.set('sort', 'popular'); // Default sort
    }

    // Handle category removal - don't add it back to the URL as ALL is the default
    // and doesn't need to be in the URL

    // Update the URL
    router.push(`${window.location.pathname}?${params.toString()}`, {
      scroll: false,
    });
  };

  const formatSortLabel = (sortValue: string): string => {
    switch (sortValue) {
      case 'newest':
        return 'Latest';
      case 'oldest':
        return 'Oldest';
      case 'popular':
        return 'Popular';
      default:
        return sortValue.charAt(0).toUpperCase() + sortValue.slice(1);
    }
  };

  return (
    <div className="mb-6 flex flex-wrap gap-2">
      {/* Level Chip */}
      {level && (
        <div className="flex items-center gap-2 rounded-md bg-blue-100 px-3 py-2">
          <div className="flex items-center gap-2">
            {type === 'town-hall' ? (
              <Image
                src={`/images/TownHalls/th${level}.png`}
                alt={`TH${level}`}
                width={20}
                height={20}
                className="size-5"
              />
            ) : (
              <Image
                src={`/images/BuilderHalls/bh${level}.png`}
                alt={`BH${level}`}
                width={20}
                height={20}
                className="size-5"
              />
            )}
            <span className="font-mono text-sm text-black">
              {type === 'town-hall' ? 'TH' : 'BH'}
              {level}
            </span>
          </div>
          <button
            onClick={() => removeFilter('level')}
            className="ml-1 rounded-full bg-black p-1 text-white transition-all duration-300 ease-in-out hover:bg-blue-200 hover:text-gray-700"
            aria-label={`Remove ${type === 'town-hall' ? 'Town Hall' : 'Builder Hall'} ${level} filter`}
          >
            <X className="size-3" />
          </button>
        </div>
      )}

      {/* Category Chip - Only for Town Hall and not for ALL category */}
      {type === 'town-hall' && category && category !== 'ALL' && (
        <div className="flex items-center gap-2 rounded-md bg-blue-100 px-3 py-2">
          <span className="text-sm font-medium text-black">
            {category.charAt(0) + category.slice(1).toLowerCase()} Base
          </span>
          <button
            onClick={() => removeFilter('category')}
            className="ml-1 rounded-full bg-black p-1 text-white transition-all duration-300 ease-in-out hover:bg-blue-200 hover:text-gray-700"
            aria-label={`Remove ${category} category filter`}
          >
            <X className="size-3" />
          </button>
        </div>
      )}

      {/* Sort Chip */}
      {sort && (
        <div className="flex items-center gap-2 rounded-md bg-blue-100 px-3 py-2">
          <span className="font-mono text-sm font-medium text-black">
            Sort By: {formatSortLabel(sort)}
          </span>
          <button
            onClick={() => removeFilter('sort')}
            className="ml-1 rounded-full bg-black p-1 text-white transition-all duration-300 ease-in-out hover:bg-blue-200 hover:text-gray-700"
            aria-label={`Remove sort by ${sort} filter`}
          >
            <X className="size-3" />
          </button>
        </div>
      )}
    </div>
  );
}
