'use client';

import { ReactNode } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';

import {
  HoverSlider,
  HoverSliderImage,
  HoverSliderImageWrap,
  TextStaggerHover,
} from '@/components/ui/animated-slideshow';
import { InfiniteSlider } from '@/components/ui/infinite-slider';

interface BounceCardProps {
  className?: string;
  children: ReactNode;
}

interface CardTitleProps {
  children: ReactNode;
}

const SLIDES = [
  {
    id: 'slide-1',
    title: 'Farming Bases',
    imageUrl: '/images/farming-base.jpg',
  },
  {
    id: 'slide-2',
    title: 'War Bases',
    imageUrl: '/images/war-base.png',
  },
  {
    id: 'slide-6',
    title: 'Trophy Bases',
    imageUrl: '/images/trophy-base.jpg',
  },
  {
    id: 'slide-3',
    title: 'Anti 2/3* Bases',
    imageUrl: '/images/anti2start.jpg',
  },
  {
    id: 'slide-4',
    title: 'Builder Hall Bases',
    imageUrl: '/images/builderHallBase1.jpg',
  },
];

export const BouncyCardsFeatures = () => {
  return (
    <section className="container mx-auto px-4 py-8 text-slate-800 md:py-16">
      <div className="mb-8 flex flex-col items-start justify-between gap-4 md:flex-row md:items-end md:px-8">
        <h2 className="heading mb-12 text-4xl font-bold text-white md:text-5xl">
          Dominate with our
          <span className=" grit-mask text-[#ffb519]">
            {' '}
            expert base layouts
          </span>
        </h2>
      </div>
      <div className="flex flex-col gap-8 md:flex-row md:gap-4">
        <div className="w-full md:w-3/4">
          <HoverSlider className="place-content-center rounded-2xl bg-slate-100 p-8 text-[#3d3929] md:px-12">
            <div className="grid h-full grid-cols-1 items-center gap-6 md:grid-cols-2">
              <div className="flex  flex-col space-y-2 md:space-y-4   ">
                {SLIDES.map((slide, index) => (
                  <TextStaggerHover
                    key={slide.title}
                    index={index}
                    className="cursor-pointer text-4xl font-black uppercase tracking-tighter"
                    text={slide.title}
                  />
                ))}
              </div>
              <HoverSliderImageWrap>
                {SLIDES.map((slide, index) => (
                  <div key={slide.id} className="  ">
                    <HoverSliderImage
                      index={index}
                      imageUrl={slide.imageUrl}
                      src={slide.imageUrl}
                      alt={slide.title}
                      className="size-full max-h-96 object-cover"
                      loading="eager"
                      decoding="async"
                    />
                  </div>
                ))}
              </HoverSliderImageWrap>
            </div>
          </HoverSlider>
        </div>
        <div className=" w-full md:w-1/4">
          <div className=" flex h-[360px] justify-center space-x-8 [mask-image:linear-gradient(to_bottom,transparent,black_10%,black_90%,transparent)] md:h-[416px]">
            <div className="flex flex-col items-center">
              <InfiniteSlider direction="vertical">
                <div className="relative aspect-square w-[150px] overflow-hidden rounded-[8px]">
                  <Image
                    src="/images/TownHalls/th3.png"
                    alt="Town Hall 3"
                    fill
                    className="object-contain"
                  />
                </div>
                <div className="relative aspect-square w-[150px] overflow-hidden rounded-[8px]">
                  <Image
                    src="/images/TownHalls/th5.png"
                    alt="Town Hall 5"
                    fill
                    className="object-contain"
                  />
                </div>
                <div className="relative aspect-square w-[150px] overflow-hidden rounded-[8px]">
                  <Image
                    src="/images/TownHalls/th7.png"
                    alt="Town Hall 7"
                    fill
                    className="object-contain"
                  />
                </div>
                <div className="relative aspect-square w-[150px] overflow-hidden rounded-[8px]">
                  <Image
                    src="/images/TownHalls/th9.png"
                    alt="Town Hall 9"
                    fill
                    className="object-contain"
                  />
                </div>
                <div className="relative aspect-square w-[150px] overflow-hidden rounded-[8px]">
                  <Image
                    src="/images/TownHalls/TH11.png"
                    alt="Town Hall 11"
                    fill
                    className="object-contain"
                  />
                </div>
                <div className="relative aspect-square w-[150px] overflow-hidden rounded-[8px]">
                  <Image
                    src="/images/TownHalls/TH13.png"
                    alt="Town Hall 13"
                    fill
                    className="object-contain"
                  />
                </div>
                <div className="relative aspect-square w-[150px] overflow-hidden rounded-[8px]">
                  <Image
                    src="/images/TownHalls/TH15.png"
                    alt="Town Hall 15"
                    fill
                    className="object-contain"
                  />
                </div>
                <div className="relative aspect-square w-[150px] overflow-hidden rounded-[8px]">
                  <Image
                    src="/images/TownHalls/th17.png"
                    alt="Town Hall 17"
                    fill
                    className="object-contain"
                  />
                </div>
              </InfiniteSlider>
            </div>

            <div className="flex flex-col items-center">
              <InfiniteSlider direction="vertical" reverse>
                <div className="relative aspect-square w-[150px] overflow-hidden rounded-[8px]">
                  <Image
                    src="/images/BuilderHalls/bh3.png"
                    alt="Builder Hall 3"
                    fill
                    className="object-contain"
                  />
                </div>
                <div className="relative aspect-square w-[150px] overflow-hidden rounded-[8px]">
                  <Image
                    src="/images/BuilderHalls/bh4.png"
                    alt="Builder Hall 4"
                    fill
                    className="object-contain"
                  />
                </div>
                <div className="relative aspect-square w-[150px] overflow-hidden rounded-[8px]">
                  <Image
                    src="/images/BuilderHalls/bh5.png"
                    alt="Builder Hall 5"
                    fill
                    className="object-contain"
                  />
                </div>
                <div className="relative aspect-square w-[150px] overflow-hidden rounded-[8px]">
                  <Image
                    src="/images/BuilderHalls/bh6.png"
                    alt="Builder Hall 6"
                    fill
                    className="object-contain"
                  />
                </div>
                <div className="relative aspect-square w-[150px] overflow-hidden rounded-[8px]">
                  <Image
                    src="/images/BuilderHalls/bh7.png"
                    alt="Builder Hall 7"
                    fill
                    className="object-contain"
                  />
                </div>
                <div className="relative aspect-square w-[150px] overflow-hidden rounded-[8px]">
                  <Image
                    src="/images/BuilderHalls/bh8.png"
                    alt="Builder Hall 8"
                    fill
                    className="object-contain"
                  />
                </div>
                <div className="relative aspect-square w-[150px] overflow-hidden rounded-[8px]">
                  <Image
                    src="/images/BuilderHalls/bh9.png"
                    alt="Builder Hall 9"
                    fill
                    className="object-contain"
                  />
                </div>
                <div className="relative aspect-square w-[150px] overflow-hidden rounded-[8px]">
                  <Image
                    src="/images/BuilderHalls/bh10.png"
                    alt="Builder Hall 10"
                    fill
                    className="object-contain"
                  />
                </div>
              </InfiniteSlider>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

// Export these components so they can be used elsewhere
export const BounceCard = ({ className, children }: BounceCardProps) => {
  return (
    <motion.div
      whileHover={{ scale: 0.95, rotate: '-1deg' }}
      className={`group relative min-h-[300px] cursor-pointer overflow-hidden rounded-2xl bg-slate-100 p-8 ${className}`}
    >
      {children}
    </motion.div>
  );
};

export const CardTitle = ({ children }: CardTitleProps) => {
  return (
    <h3 className="mx-auto text-center text-3xl font-semibold">{children}</h3>
  );
};
