'use client';

import * as React from 'react';
import { useRef, useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { MousePointerClick } from 'lucide-react';

import type { ButtonProps } from '@/components/ui/button';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface ParticleButtonProps extends ButtonProps {
  onSuccess?: () => void;
  successDuration?: number;
}

function SuccessParticles({
  buttonRef,
}: {
  buttonRef: React.RefObject<HTMLButtonElement | null>;
}) {
  const rect = buttonRef.current?.getBoundingClientRect();
  if (!rect) return null;

  const centerX = rect.left + rect.width / 2;
  const centerY = rect.top + rect.height / 2;

  return (
    <AnimatePresence>
      {[...Array(6)].map((_, i) => (
        <motion.div
          key={i}
          className="fixed size-1 rounded-full bg-black dark:bg-white"
          style={{ left: centerX, top: centerY }}
          initial={{
            scale: 0,
            x: 0,
            y: 0,
          }}
          animate={{
            scale: [0, 1, 0],
            x: [0, (i % 2 ? 1 : -1) * (Math.random() * 50 + 20)],
            y: [0, -Math.random() * 50 - 20],
          }}
          transition={{
            duration: 0.6,
            delay: i * 0.1,
            ease: 'easeOut',
          }}
        />
      ))}
    </AnimatePresence>
  );
}

function ParticleButton({
  children,
  // Unused props, but keeping them in the interface for future use
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  onClick,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  onSuccess,
  successDuration = 1000,
  className,
  ...props
}: ParticleButtonProps) {
  const [showParticles, setShowParticles] = useState(false);
  const buttonRef = useRef<HTMLButtonElement>(null);

  const handleClick = async () => {
    setShowParticles(true);

    setTimeout(() => {
      setShowParticles(false);
    }, successDuration);
  };

  return (
    <>
      {showParticles && <SuccessParticles buttonRef={buttonRef} />}
      <Button
        ref={buttonRef}
        onClick={handleClick}
        className={cn(
          'relative',
          showParticles && 'scale-95',
          'transition-transform duration-100',
          className
        )}
        {...props}
      >
        {children}
        <MousePointerClick className="size-4" />
      </Button>
    </>
  );
}

export { ParticleButton };
