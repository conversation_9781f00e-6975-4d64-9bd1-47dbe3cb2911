'use client';

import { useRouter } from 'next/navigation';

export function BackButton() {
  const router = useRouter();
  return (
    <button
      type="button"
      className="focus:ring-ring bg-primary text-primary-foreground hover:bg-primary/80 flex items-center gap-2 rounded-md border border-transparent px-3 py-2 text-sm font-light shadow transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2"
      onClick={() => router.back()}
    >
      <svg
        className="size-4"
        width="20"
        height="20"
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M15 19l-7-7 7-7"
        />
      </svg>
      Back
    </button>
  );
}
