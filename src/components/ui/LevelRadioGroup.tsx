import React from 'react';

import { RadioGroup, RadioGroupItem } from './SmallRadioGroup';

interface LevelRadioGroupProps {
  levels: number[];
  value: number;
  onChange: (level: number) => void;
  name: string;
  disabledLevels?: number[];
}

export function LevelRadioGroup({
  levels,
  value,
  onChange,
  name,
  disabledLevels = [],
}: LevelRadioGroupProps) {
  return (
    <RadioGroup
      name={name}
      value={String(value)}
      onValueChange={(val) => onChange(Number(val))}
      className="mt-2 flex flex-wrap gap-3"
    >
      {levels.map((level) => (
        <RadioGroupItem
          key={level}
          value={String(level)}
          aria-label={`Level ${level}`}
          disabled={disabledLevels.includes(level)}
          className={`flex h-14  min-w-[60px] items-center justify-center rounded-xl border text-lg font-semibold transition-all
            ${value === level ? 'border-primary ring-primary bg-white text-black ring-2' : 'hover:border-primary border-gray-300 bg-white text-black'}
            ${disabledLevels.includes(level) ? 'cursor-not-allowed opacity-50' : ''}`}
        >
          {level}
        </RadioGroupItem>
      ))}
    </RadioGroup>
  );
}
