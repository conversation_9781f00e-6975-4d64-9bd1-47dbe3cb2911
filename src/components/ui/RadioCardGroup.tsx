import React from 'react';

export type RadioCardOption = {
  value: string;
  label: string;
  description?: string;
  icon?: React.ReactNode;
};

interface RadioCardGroupProps {
  name: string;
  options: RadioCardOption[];
  value: string;
  onChange: (value: string) => void;
}

export function RadioCardGroup({
  name,
  options,
  value,
  onChange,
}: RadioCardGroupProps) {
  return (
    <div className="flex flex-wrap gap-4">
      {options.map((option) => (
        <label
          key={option.value}
          htmlFor={`${name}-${option.value}`}
          className={`relative flex cursor-pointer flex-col rounded-lg border bg-white p-5 shadow-md transition-all ${
            value === option.value
              ? 'border-green-500 ring-2 ring-green-200'
              : 'border-gray-200'
          }`}
        >
          <span className="font-semibold uppercase leading-tight text-gray-500">
            {option.label}
          </span>
          {option.icon && <span className="mb-2">{option.icon}</span>}
          {option.description && (
            <span className="mb-2 text-gray-700">{option.description}</span>
          )}
          <input
            type="radio"
            name={name}
            id={`${name}-${option.value}`}
            value={option.value}
            checked={value === option.value}
            onChange={() => onChange(option.value)}
            className="absolute size-0 appearance-none"
          />
          {value === option.value && (
            <span
              aria-hidden="true"
              className="pointer-events-none absolute inset-0 rounded-lg border-2 border-green-500 bg-green-200/10"
            >
              <span className="absolute right-4 top-4 inline-flex size-6 items-center justify-center rounded-full bg-green-200">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  className="size-5 text-green-600"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
              </span>
            </span>
          )}
        </label>
      ))}
    </div>
  );
}
