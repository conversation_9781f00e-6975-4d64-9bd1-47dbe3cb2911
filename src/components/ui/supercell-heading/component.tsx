import type { ReactNode } from 'react';

import { cn } from '@/lib/utils';

interface SupercellHeadingProps {
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
  children: ReactNode;
  className?: string;
}

/**
 * A component that renders headings with the Supercell font
 * This makes it easy to consistently apply the Supercell font to headings
 */
export const SupercellHeading = ({
  as = 'h2',
  children,
  className,
}: SupercellHeadingProps) => {
  const Component = as;

  return (
    <Component className={cn('font-supercell', className)}>
      {children}
    </Component>
  );
};
