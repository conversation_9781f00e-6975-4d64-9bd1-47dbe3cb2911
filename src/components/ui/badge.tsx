'use client';

import { cn } from '@/lib/utils';

interface BadgeProps {
  variant?: 'default' | 'secondary' | 'outline' | 'destructive';
  children: React.ReactNode;
  className?: string;
}

export function Badge({
  variant = 'default',
  children,
  className,
  ...props
}: BadgeProps) {
  return (
    <span
      className={cn(
        'inline-flex items-center rounded-md px-2 py-1 text-xs font-medium',
        {
          'bg-primary text-primary-foreground': variant === 'default',
          'bg-secondary text-secondary-foreground': variant === 'secondary',
          'border border-border bg-background text-foreground':
            variant === 'outline',
          'bg-destructive text-destructive-foreground':
            variant === 'destructive',
        },
        className
      )}
      {...props}
    >
      {children}
    </span>
  );
}
