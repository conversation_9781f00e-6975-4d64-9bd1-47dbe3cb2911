'use client';
import { useState } from 'react';
import { LuLogIn, LuLogOut, LuMoon, LuSun } from 'react-icons/lu';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { signOut, useSession } from 'next-auth/react';

interface NavItem {
  name: string;
  href: string;
}

const navItems: NavItem[] = [
  { name: 'Town Hall', href: '/town-hall' },
  { name: 'Builder Hall', href: '/builder-hall' },
  { name: 'Add Base', href: '/add-base' },
  { name: 'Admin', href: '/admin' },
];

export function Header() {
  const pathname = usePathname();
  const [isDarkMode, setIsDarkMode] = useState(false);
  const { data: session, status } = useSession();
  const isAuthenticated = status === 'authenticated';

  const toggleDarkMode = () => {
    const newMode = !isDarkMode;
    setIsDarkMode(newMode);
    document.documentElement.classList.toggle('dark', newMode);
  };

  const handleLogout = async () => {
    await signOut({ redirect: true, callbackUrl: '/' });
  };

  return (
    <header className="header">
      <div className="header-inner layout">
        <div className="flex items-center gap-6">
          <Link href="/" className="text-xl font-bold">
            CoC Bases
          </Link>
          <nav className="hidden items-center space-x-4 md:flex">
            {navItems.map((item) => {
              const isActive =
                pathname === item.href || pathname.startsWith(`${item.href}/`);
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`sidebar-nav-item ${isActive ? 'active' : ''}`}
                >
                  {item.name}
                </Link>
              );
            })}
          </nav>
        </div>
        <div className="flex items-center space-x-4">
          {/* Authentication buttons */}
          {isAuthenticated ? (
            <div className="flex items-center gap-4">
              <span className="hidden text-sm text-gray-600 md:inline-block dark:text-gray-300">
                {session?.user?.name || 'User'}
              </span>
              <button
                onClick={handleLogout}
                className="flex items-center gap-1 rounded-md px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-neutral-800"
                aria-label="Sign out"
              >
                <LuLogOut className="size-4" />
                <span className="hidden md:inline-block">Logout</span>
              </button>
            </div>
          ) : (
            <Link
              href="/login"
              className="flex items-center gap-1 rounded-md px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-neutral-800"
            >
              <LuLogIn className="size-4" />
              <span className="hidden md:inline-block">Login</span>
            </Link>
          )}

          {/* Theme toggle */}
          <button
            onClick={toggleDarkMode}
            className="hover:bg-accent rounded-md p-2"
            aria-label={
              isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'
            }
          >
            {isDarkMode ? (
              <LuSun className="size-5" />
            ) : (
              <LuMoon className="size-5" />
            )}
          </button>
        </div>
      </div>
    </header>
  );
}
