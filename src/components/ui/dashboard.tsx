'use client';

interface CardProps {
  title?: string;
  description?: string;
  footer?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
}

export function Card({
  title,
  description,
  footer,
  children,
  className = '',
}: CardProps) {
  return (
    <div className={`card ${className}`}>
      <div className="card-content">
        {(title || description) && (
          <div className="card-header">
            {title && <h3 className="font-supercell card-title">{title}</h3>}
            {description && <p className="card-description">{description}</p>}
          </div>
        )}
        <div className="card-body">{children}</div>
        {footer && <div className="card-footer">{footer}</div>}
      </div>
    </div>
  );
}

interface DashboardProps {
  children: React.ReactNode;
}

export function Dashboard({ children }: DashboardProps) {
  return <div className="dashboard">{children}</div>;
}

interface DashboardHeaderProps {
  title: string;
  description?: string;
  actions?: React.ReactNode;
}

export function DashboardHeader({
  title,
  description,
  actions,
}: DashboardHeaderProps) {
  return (
    <div className="dashboard-header">
      <div>
        <h1 className="font-supercell dashboard-title">{title}</h1>
        {description && <p className="dashboard-description">{description}</p>}
      </div>
      {actions && <div className="dashboard-actions">{actions}</div>}
    </div>
  );
}

interface DashboardSectionProps {
  children: React.ReactNode;
  className?: string;
}

export function DashboardSection({
  children,
  className = '',
}: DashboardSectionProps) {
  return <div className={`dashboard-section ${className}`}>{children}</div>;
}
