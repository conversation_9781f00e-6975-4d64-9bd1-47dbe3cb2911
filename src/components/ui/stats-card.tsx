'use client';

import { LuArrowUpRight } from 'react-icons/lu';

interface StatsCardProps {
  title: string;
  value: string | number;
  change?: {
    value: string | number;
    trend: 'up' | 'down' | 'neutral';
  };
  description?: string;
}

export function StatsCard({
  title,
  value,
  change,
  description,
}: StatsCardProps) {
  return (
    <div className="stats-card">
      <div className="flex flex-col">
        <div className="text-muted-foreground text-sm font-medium">{title}</div>
        <div className="mt-1 flex items-baseline">
          <div className="text-3xl font-semibold">{value}</div>
          {change && (
            <div className="ml-2 flex items-baseline text-sm">
              <span
                className={
                  change.trend === 'up'
                    ? 'text-green-500'
                    : change.trend === 'down'
                      ? 'text-red-500'
                      : 'text-muted-foreground'
                }
              >
                {change.value}
                {change.trend === 'up' && (
                  <LuArrowUpRight className="ml-0.5 inline size-4" />
                )}
                {change.trend === 'down' && (
                  <LuArrowUpRight className="ml-0.5 inline size-4 rotate-180" />
                )}
              </span>
            </div>
          )}
        </div>
        {description && (
          <div className="text-muted-foreground mt-1 text-sm">
            {description}
          </div>
        )}
      </div>
    </div>
  );
}

export function StatsGrid({ children }: { children: React.ReactNode }) {
  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
      {children}
    </div>
  );
}
