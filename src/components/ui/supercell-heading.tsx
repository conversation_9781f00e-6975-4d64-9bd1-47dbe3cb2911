import React from 'react';

import { cn } from '@/lib/utils';

interface SupercellHeadingProps {
  children: React.ReactNode;
  className?: string;
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
}

export function SupercellHeading({
  children,
  className,
  as: Component = 'h2',
}: SupercellHeadingProps) {
  return (
    <Component
      className={cn('font-supercell text-2xl tracking-tight', className)}
    >
      {children}
    </Component>
  );
}
