'use client';
import { useState } from 'react';

const BH_LEVELS = Array.from({ length: 8 }, (_, i) => 3 + i); // [3,4,...10]
const BASE_TYPES = ['Trophy', 'Versus', 'Hybrid'];

export default function AddBuilderBaseForm() {
  const [image, setImage] = useState<File | null>(null);
  const [title, setTitle] = useState('');
  const [link, setLink] = useState('');
  const [bhLevel, setBhLevel] = useState(3);
  const [type, setType] = useState(BASE_TYPES[0]);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    setSubmitting(true);
    setError(null);
    setSuccess(false);
    const formData = new FormData();
    if (image) formData.append('image', image);
    formData.append('title', title);
    formData.append('link', link);
    formData.append('bhLevel', String(bhLevel));
    formData.append('type', type);
    try {
      const res = await fetch('/api/builder-base', {
        method: 'POST',
        body: formData,
      });
      if (!res.ok) throw new Error('Failed to add builder base');
      setSuccess(true);
      setTitle('');
      setLink('');
      setBhLevel(3);
      setType(BASE_TYPES[0]);
      setImage(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setSubmitting(false);
    }
  }

  return (
    <form className="space-y-6" onSubmit={handleSubmit}>
      <div>
        <label htmlFor="image" className="mb-1 block font-medium">
          Image
        </label>
        <input
          id="image"
          type="file"
          accept="image/*"
          onChange={(e) => setImage(e.target.files?.[0] || null)}
          required
        />
      </div>
      <div>
        <label htmlFor="title" className="mb-1 block font-medium">
          Layout Title
        </label>
        <input
          id="title"
          className="input input-bordered w-full"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          required
        />
      </div>
      <div>
        <label htmlFor="link" className="mb-1 block font-medium">
          Clash of Clans Builder Base Link
        </label>
        <input
          id="link"
          className="input input-bordered w-full"
          value={link}
          onChange={(e) => setLink(e.target.value)}
          required
        />
      </div>
      <div>
        <label htmlFor="bhLevel" className="mb-1 block font-medium">
          Builder Hall Level
        </label>
        <select
          id="bhLevel"
          className="select select-bordered w-full"
          value={bhLevel}
          onChange={(e) => setBhLevel(Number(e.target.value))}
        >
          {BH_LEVELS.map((lvl) => (
            <option key={lvl} value={lvl}>
              BH{lvl}
            </option>
          ))}
        </select>
      </div>
      <fieldset className="mb-1 block font-medium">
        <legend className="mb-1">Base Type</legend>
        <div className="flex gap-4">
          {BASE_TYPES.map((t) => (
            <label
              key={t}
              className="flex items-center gap-1"
              htmlFor={`baseType-${t}`}
            >
              <input
                type="radio"
                name="baseType"
                value={t}
                checked={type === t}
                onChange={() => setType(t)}
                id={`baseType-${t}`}
              />
              {t}
            </label>
          ))}
        </div>
      </fieldset>
      <button
        type="submit"
        className="btn btn-primary w-full"
        disabled={submitting}
      >
        {submitting ? 'Submitting...' : 'Add Builder Base'}
      </button>
      {error && <div className="text-red-500">{error}</div>}
      {success && (
        <div className="text-green-600">Builder Base added successfully!</div>
      )}
    </form>
  );
}
