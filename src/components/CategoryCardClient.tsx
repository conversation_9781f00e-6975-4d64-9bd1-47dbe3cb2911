'use client';
import { useCallback, useEffect, useState } from 'react';
import Image from 'next/image';

import { createTimeoutSignal, formatViewCount } from '@/lib/utils';

export default function CategoryCardClient({
  type,
  level,
  count,
  views,
  href,
  label,
  baseLabel,
}: {
  type: 'TOWN_HALL' | 'BUILDER_HALL';
  level: number;
  count: number;
  views: number;
  href: string;
  label: string;
  baseLabel: string;
}) {
  const [localViews, setLocalViews] = useState(views);
  const [isHydrated, setIsHydrated] = useState(false);
  const [clicked, setClicked] = useState(false);
  // Commented out unused variables
  // const { hasBeenViewed } = useViewTracker();
  // Create a tracking key that matches the one used in CategoryViewManager
  // const trackingKey = `category:${type}:${level}`;

  // Define the fetch function with useCallback to prevent unnecessary re-renders
  const fetchLatestViewCount = useCallback(async () => {
    if (!isHydrated) return;

    try {
      // Use multiple cache-busting techniques
      const timestamp = new Date().getTime();
      const randomVal = Math.random().toString(36).substring(2, 15);

      // Add an incrementing counter for repeated calls without using 'any'
      const w = window as Window &
        typeof globalThis & {
          _fetchCounter?: number;
        };
      const uniqueCounter = (w._fetchCounter = (w._fetchCounter || 0) + 1);

      const res = await fetch(
        `/api/visits/category/views?type=${type}&level=${level}&_=${timestamp}&r=${randomVal}&c=${uniqueCounter}`,
        {
          // Strict no-cache settings to ensure fresh data
          cache: 'no-store',
          headers: {
            Pragma: 'no-cache',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            // Add a unique request ID header
            'X-Request-ID': `${timestamp}-${randomVal}`,
          },
          next: { revalidate: 0 },
          // Add a timeout to prevent hanging requests - increased from 5s to 10s
          signal: createTimeoutSignal(10000),
        }
      );
      if (res.ok) {
        const data = await res.json();
        if (typeof data.views === 'number') {
          setLocalViews(data.views);
        }
      } else {
        // Silently fail for timeout errors - they're expected occasionally
        if (res.status !== 408 && res.statusText !== 'timeout') {
          console.warn(
            `View count fetch returned ${res.status} for ${type} level ${level}`
          );
        }
        // Use the existing view count as fallback
      }
    } catch (err: unknown) {
      // Only log to console but don't break the UI
      // Don't log timeout errors at all - they're expected occasionally
      if (err instanceof Error) {
        if (err.name !== 'AbortError' && err.name !== 'TimeoutError') {
          console.warn('Non-critical error fetching view count:', err);
        } else if (process.env.NODE_ENV === 'development') {
          // In development, log a more subtle message for timeouts
          console.debug('View count fetch timed out - using cached value');
        }
      }
    }
  }, [isHydrated, type, level]);

  // Mark component as hydrated after initial render
  useEffect(() => {
    setIsHydrated(true);

    // Register page visibility change listener
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        // When user returns to the page, force a refresh of view counts
        setTimeout(() => {
          fetchLatestViewCount();
        }, 300);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [fetchLatestViewCount]);

  // Fetch the latest view count when the component mounts or on focus
  useEffect(() => {
    // Don't do anything until hydration is complete to prevent UI flicker
    if (!isHydrated) return;

    // Track if the component is mounted to avoid state updates after unmount
    let isMounted = true;

    // Add a small delay to let the page stabilize before fetching
    const initialFetchTimer = setTimeout(() => {
      // Only fetch if component is still mounted
      if (isMounted) {
        // Fetch latest count regardless of whether viewed before
        // This ensures consistent counts across the site
        fetchLatestViewCount();
      }
    }, 300);

    // Use a debounced version of the function for the focus event
    // to prevent multiple calls when focus changes rapidly
    let focusTimer: NodeJS.Timeout;
    const handleFocus = () => {
      clearTimeout(focusTimer);
      focusTimer = setTimeout(() => {
        // Always fetch latest view count on focus to ensure consistency
        fetchLatestViewCount();
      }, 300); // 300ms debounce
    };

    // Add an event listener to update the view count when the window gets focus
    window.addEventListener('focus', handleFocus);

    return () => {
      window.removeEventListener('focus', handleFocus);
      clearTimeout(focusTimer);
      clearTimeout(initialFetchTimer);
      isMounted = false;
    };
  }, [type, level, isHydrated, fetchLatestViewCount]);

  const handleClick = async () => {
    if (clicked) return;
    setClicked(true);

    // We're bypassing the view check entirely for testing
    // Always increment the view regardless of previous views
    setLocalViews((v) => v + 1);

    // Send asynchronous request to increment the visit count on the backend
    try {
      // Add unique timestamp and random value to prevent caching
      const timestamp = Date.now();
      const random = Math.random().toString(36).substring(2, 10);

      // Use a URL with query parameters to completely bypass any POST caching
      const bypassCacheUrl = `/api/visits/category?type=${type}&level=${level}&_t=${timestamp}&_r=${random}`;

      const response = await fetch(bypassCacheUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          Pragma: 'no-cache',
          'X-Force-Unique': `${timestamp}-${random}`, // Force unique request
        },
        body: JSON.stringify({
          type,
          level,
          _timestamp: timestamp, // Add timestamp to make request unique
          _nonce: random, // Add random nonce to make request unique
          force: true, // Tell the server to force increment
        }),
        cache: 'no-store',
      });

      if (!response.ok) {
        console.warn(
          `Failed to update visit count for ${type} level ${level}: ${response.status}`
        );
        // Revert optimistic update if the request fails
        setLocalViews((v) => v - 1);
      }
    } catch (error) {
      console.error('Error updating visit count:', error);
      // Revert optimistic update if the request fails
      setLocalViews((v) => v - 1);
    }
    // Additional tracking will be handled by CategoryViewManager in the destination page
  };

  return (
    <a
      href={href}
      className="group relative flex flex-col items-center overflow-hidden rounded-lg border-[0.5px] border-solid !border-[#5AAF1B] p-4 shadow transition hover:shadow-lg"
      style={{
        backgroundImage: "url('/images/egypt-scenery.png')",
        backgroundSize: 'cover',
        backgroundPosition: 'center',
      }}
      onClick={handleClick}
    >
      {/* Add a blurry gradient overlay to improve content visibility */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#1a3a1a]/70 to-[#335533]/50 backdrop-blur-[1px]" />
      {/* Position content above the background */}
      <div className="relative z-10 flex flex-col items-center">
        <Image
          src={
            type === 'BUILDER_HALL'
              ? `/images/BuilderHalls/bh${level}.png`
              : `/images/TownHalls/th${level}.png` // Use lowercase as that's what exists in the directory
          }
          alt={label}
          width={128}
          height={128}
          className="mb-2 size-32 object-contain transition group-hover:scale-105"
          loading="lazy"
          unoptimized
          onError={(e) => {
            // If lowercase th didn't work, try uppercase TH
            if (type === 'TOWN_HALL') {
              e.currentTarget.src = `/images/TownHalls/TH${level}.png`;
              e.currentTarget.onerror = () => {
                // If both failed, use th15 as fallback (we know it exists)
                const fallbackImagePath = '/images/TownHalls/th15.png';
                e.currentTarget.src = fallbackImagePath;
                e.currentTarget.onerror = null;
              };
              return;
            }

            // For BuilderHall, use fallback directly
            const fallbackImagePath =
              type === 'BUILDER_HALL'
                ? '/images/BuilderHalls/bh9.png' // Use one that definitely exists
                : '/images/TownHalls/th15.png'; // Use lowercase as that's what exists in the directory

            e.currentTarget.src = fallbackImagePath;
            console.log(
              `Using fallback image for ${type} level ${level}: ${fallbackImagePath}`
            );

            // In case even the fallback fails
            e.currentTarget.onerror = null; // Prevent infinite loop
          }}
        />
        <div className="font-supercell mb-1 text-lg text-white">{label}</div>
        <span className="baseLevel grit-mask font-supercell">{level}</span>

        <div className="flex flex-wrap items-center gap-2">
          {/* Number of bases */}
          <div className="font-supercell flex items-center gap-1 rounded-[8px] bg-white px-2 py-[3.2px] text-xs font-medium text-[#161616]">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              className="injected-svg"
              data-src="https://cdn.hugeicons.com/icons/home-01-solid-rounded.svg"
              xmlnsXlink="http://www.w3.org/1999/xlink"
              role="img"
              color="#000000"
            >
              <path
                d="M12 1.75C12.7872 1.75 13.4813 2.01975 14.2119 2.45996C14.9179 2.88535 15.7234 3.51175 16.7265 4.29199L16.7578 4.31641L18.7578 5.87207L19.6299 6.55566C20.4227 7.19264 20.9798 7.72021 21.3174 8.41016L21.3965 8.58496C21.7653 9.46446 21.75 10.4822 21.75 11.9893V14.5576C21.75 16.1592 21.75 17.4378 21.6152 18.4404C21.4846 19.4121 21.2165 20.223 20.6269 20.877L20.5049 21.0049C19.8297 21.68 18.9766 21.9759 17.9404 22.1152C16.9378 22.25 15.6591 22.25 14.0576 22.25H9.94235C8.34076 22.25 7.06218 22.25 6.05954 22.1152C5.08787 21.9846 4.27692 21.7165 3.62301 21.127L3.49509 21.0049C2.81998 20.3298 2.52405 19.4767 2.38473 18.4404C2.28242 17.6794 2.25787 16.7596 2.25192 15.6602L2.24997 14.5V11.9893C2.24997 10.3817 2.23258 9.33028 2.68259 8.41016C3.13269 7.49024 3.97332 6.85894 5.24216 5.87207L7.24216 4.31641L7.96872 3.75293C8.66073 3.22012 9.25301 2.78235 9.78805 2.45996C10.5187 2.01974 11.2128 1.75002 12 1.75ZM15.789 16.3857C15.471 15.9773 14.899 15.883 14.4697 16.1523L14.3857 16.2109C13.765 16.694 12.9316 17 12 17C11.1847 16.9999 10.4445 16.7661 9.85641 16.3838L9.61423 16.2109L9.53024 16.1523C9.1009 15.8832 8.52891 15.9772 8.21091 16.3857C7.89294 16.7944 7.94189 17.3726 8.30856 17.7227L8.38571 17.7891L8.57223 17.9277C9.52254 18.6047 10.7167 18.9999 12 19C13.3686 19 14.636 18.5505 15.6142 17.7891L15.6914 17.7227C16.0582 17.3725 16.1072 16.7945 15.789 16.3857Z"
                fill="#000000"
              ></path>
            </svg>
            <div className="text-xs ">
              {count} {baseLabel}
              {count === 1 ? '' : 's'}
            </div>
          </div>

          {/* Number of views */}
          <span className="font-supercell flex items-center gap-1 rounded-[8px] bg-white px-2 py-[3.2px] text-xs font-medium text-[#161616]">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              className="injected-svg size-4"
              data-src="https://cdn.hugeicons.com/icons/view-solid-standard.svg"
              xmlnsXlink="http://www.w3.org/1999/xlink"
              role="img"
              color="#000000"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M22.3815 12.1545L22.3754 12.1633C22.2302 12.372 21.8157 12.9676 21.5581 13.3017C21.0364 13.9783 20.2801 14.8824 19.3424 15.7891C17.4969 17.5738 14.814 19.5 11.75 19.5C8.68603 19.5 6.00305 17.5738 4.15756 15.7891C3.21994 14.8824 2.46362 13.9783 1.94192 13.3017C1.68461 12.968 1.27049 12.373 1.12481 12.1636L1.12477 12.1636L1.11818 12.1541C0.960607 11.9077 0.960607 11.5923 1.11818 11.3459L1.12475 11.3365C1.27037 11.1272 1.68457 10.532 1.94192 10.1983C2.46362 9.52169 3.21994 8.61758 4.15756 7.71086C6.00305 5.92619 8.68603 4 11.75 4C14.814 4 17.4969 5.92619 19.3424 7.71086C20.2801 8.61758 21.0364 9.52169 21.5581 10.1983C21.8155 10.5321 22.2298 11.1275 22.3754 11.3366L22.3818 11.3459C22.5394 11.5923 22.5391 11.9082 22.3815 12.1545ZM11.75 15.5C9.67893 15.5 8 13.8211 8 11.75C8 9.67893 9.67893 8 11.75 8C13.8211 8 15.5 9.67893 15.5 11.75C15.5 13.8211 13.8211 15.5 11.75 15.5Z"
                fill="#000000"
              ></path>
            </svg>
            {formatViewCount(localViews)}
          </span>
        </div>
      </div>
    </a>
  );
}
