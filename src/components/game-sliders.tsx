'use client';

import Image from 'next/image';
import Link from 'next/link';

import { InfiniteSlider } from '@/components/ui/infinite-slider';

/**
 * GameSliders component displays Town Hall and Builder Hall images in vertical infinite sliders
 */
export function GameSliders() {
  return (
    <div className="container py-16">
      <h2 className="heading mb-10 text-center text-3xl font-extrabold md:text-4xl">
        Find bases for every level
      </h2>

      <div className="flex flex-col items-center justify-center gap-12 md:flex-row md:gap-16">
        <div className="flex flex-col items-center">
          <div className="bg-card/20 relative h-[450px] rounded-lg p-6 shadow-xl backdrop-blur-sm">
            <div className="from-primary/10 absolute inset-0 rounded-lg bg-gradient-to-b to-transparent opacity-50"></div>
            <InfiniteSlider direction="vertical">
              {[3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17].map(
                (level) => (
                  <Link
                    href={`/base/town-hall/${level}`}
                    key={level}
                    className="bg-background/60 hover:shadow-primary/25 group relative aspect-square w-[200px] overflow-hidden rounded-lg p-3 shadow-lg transition-all hover:scale-105"
                  >
                    <div className="relative size-full">
                      <Image
                        src={`/images/TownHalls/th${level}.png`}
                        alt={`Town Hall ${level}`}
                        fill
                        className="object-contain transition-all group-hover:brightness-110"
                      />
                      <div className="bg-background/80 absolute inset-x-0 bottom-1 rounded-md py-1 text-center text-sm font-medium">
                        TH {level}
                      </div>
                    </div>
                  </Link>
                )
              )}
            </InfiniteSlider>
          </div>
        </div>

        <div className="flex flex-col items-center">
          <div className="bg-card/20 relative h-[450px] rounded-lg p-6 shadow-xl backdrop-blur-sm">
            <div className="from-secondary/10 absolute inset-0 rounded-lg bg-gradient-to-b to-transparent opacity-50"></div>
            <InfiniteSlider direction="vertical" reverse>
              {[3, 4, 5, 6, 7, 8, 9, 10].map((level) => (
                <Link
                  href={`/builder-hall/${level}`}
                  key={level}
                  className="bg-background/60 hover:shadow-secondary/25 group relative aspect-square w-[200px] overflow-hidden rounded-lg p-3 shadow-lg transition-all hover:scale-105"
                >
                  <div className="relative size-full">
                    <Image
                      src={`/images/BuilderHalls/bh${level}.png`}
                      alt={`Builder Hall ${level}`}
                      fill
                      className="object-contain transition-all group-hover:brightness-110"
                    />
                    <div className="bg-background/80 absolute inset-x-0 bottom-1 rounded-md py-1 text-center text-sm font-medium">
                      BH {level}
                    </div>
                  </div>
                </Link>
              ))}
            </InfiniteSlider>
          </div>
        </div>
      </div>
    </div>
  );
}

export default GameSliders;
