'use client';

import { LuLogIn } from 'react-icons/lu';
import Link from 'next/link';
import { useSession } from 'next-auth/react';

export const SignInButton = () => {
  const { status } = useSession();

  if (status === 'authenticated') {
    return null; // Don't show sign-in button if user is already authenticated
  }

  return (
    <Link href="/login">
      <button className="btn btn-primary font-supercell flex items-center gap-2">
        <LuLogIn className="size-4" />
        <span>Login</span>
      </button>
    </Link>
  );
};
