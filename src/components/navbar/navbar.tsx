import Link from 'next/link';

import { <PERSON>goutButton } from '@/components/navbar/logout-button';
import { SignInButton } from '@/components/navbar/sign-in-button';

export const Navbar = () => {
  return (
    <header className="w-full border-b">
      <div className="container flex h-16 items-center justify-between">
        <Link href="/" className="font-mono text-lg font-bold">
          Admin Panel
        </Link>
        <nav className="flex items-center gap-4">
          <a href="/add-base" className="hover:underline">
            Admin Panel
          </a>
        </nav>
        <div className="flex items-center gap-2">
          <SignInButton />
          <LogoutButton />
        </div>
      </div>
    </header>
  );
};
