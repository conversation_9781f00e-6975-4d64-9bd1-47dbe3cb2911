'use client';
import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import { twMerge } from 'tailwind-merge';

import { ThemeSwitcher } from '../theme-switcher';
import { SignInButton } from './sign-in-button';

function LogoutButton() {
  const handleLogout = async () => {
    const { signOut } = await import('next-auth/react');
    await signOut({ redirect: true, callbackUrl: '/' });
  };

  return (
    <button
      onClick={handleLogout}
      className="btn btn-outline flex items-center gap-2"
    >
      <svg
        className="size-5"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" />
        <polyline points="16 17 21 12 16 7" />
        <line x1="21" y1="12" x2="9" y2="12" />
      </svg>
      <span>Logout</span>
    </button>
  );
}

export const Navbar = () => {
  const { data: session, status } = useSession();

  // Check if user is admin based on role or email
  // Check if user is authenticated
  const isAuthenticated = status === 'authenticated';

  const [isOpen, setIsOpen] = useState(false);
  return (
    <>
      <header className="fixed top-0 z-50 w-full">
        <div className="container max-w-4xl">
          <div className="w-[min(90%,700px)] rounded-sm bg-black/70 backdrop-blur-md dark:bg-black/70">
            <div className="flex items-center justify-between p-2 px-4">
              <Link href="/">
                <Image
                  src="/images/logo.png"
                  alt="Logo"
                  width={2027}
                  height={931}
                  className="w-auto"
                  style={{ height: '2.75rem', width: 'auto' }}
                  priority
                />
              </Link>

              <div className="flex items-center justify-end gap-4">
                <ThemeSwitcher />
                <div>
                  {isAuthenticated ? (
                    <div className="flex items-center gap-2">
                      {session?.user?.name && (
                        <span className="hidden text-sm text-white md:block">
                          {session.user.name}
                        </span>
                      )}
                      <LogoutButton />
                    </div>
                  ) : (
                    <SignInButton />
                  )}
                  {/* Navbar toggle button */}
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="feather feather-menu hidden text-white"
                    onClick={() => setIsOpen(!isOpen)}
                  >
                    <line
                      x1="3"
                      y1="6"
                      x2="21"
                      y2="6"
                      className={twMerge(
                        'origin-left transition',
                        isOpen && 'rotate-45 -translate-y-1'
                      )}
                    ></line>
                    <line
                      x1="3"
                      y1="12"
                      x2="21"
                      y2="12"
                      className={twMerge('transition', isOpen && 'opacity-0')}
                    ></line>
                    <line
                      x1="3"
                      y1="18"
                      x2="21"
                      y2="18"
                      className={twMerge(
                        'origin-left transition',
                        isOpen && '-rotate-45 translate-y-1'
                      )}
                    ></line>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>
    </>
  );
};
