'use client';
import { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { toast } from 'sonner';

type LoginFormProps = {
  role: 'admin' | 'user';
  title: string;
  submitText: string;
  redirectPath: string;
  apiRoute: string;
  showOAuthProviders?: boolean;
  showRememberMe?: boolean;
  onLogin?: (success: boolean) => void;
};

// OAuth provider configuration
const oauthProviders = [
  {
    id: 'google',
    name: 'Google',
    color: 'bg-[#4285F4]',
    icon: (
      <svg className="size-5" viewBox="0 0 48 48">
        <g>
          <path
            fill="#4285F4"
            d="M24 9.5c3.54 0 6.7 1.22 9.19 3.22l6.85-6.85C36.18 2.36 30.47 0 24 0 14.82 0 6.73 5.82 2.69 14.09l7.98 6.2C12.36 14.13 17.73 9.5 24 9.5z"
          />
          <path
            fill="#34A853"
            d="M46.1 24.5c0-1.64-.15-3.22-.43-4.74H24v9.01h12.44c-.54 2.9-2.18 5.36-4.64 7.01l7.19 5.6C43.27 37.27 46.1 31.41 46.1 24.5z"
          />
          <path
            fill="#FBBC05"
            d="M10.67 28.29a14.5 14.5 0 0 1 0-8.58l-7.98-6.2A24.01 24.01 0 0 0 0 24c0 3.77.9 7.34 2.69 10.49l7.98-6.2z"
          />
          <path
            fill="#EA4335"
            d="M24 48c6.47 0 12.18-2.14 16.19-5.82l-7.19-5.6c-2.01 1.35-4.59 2.15-9 2.15-6.27 0-11.64-4.63-13.33-10.79l-7.98 6.2C6.73 42.18 14.82 48 24 48z"
          />
        </g>
      </svg>
    ),
  },
  {
    id: 'facebook',
    name: 'Facebook',
    color: 'bg-[#1877F3]',
    icon: (
      <svg className="size-5" viewBox="0 0 24 24">
        <path
          fill="#1877F3"
          d="M22.675 0h-21.35C.595 0 0 .592 0 1.326v21.348C0 23.408.595 24 1.326 24h11.495v-9.294H9.691v-3.622h3.13V8.413c0-3.1 1.893-4.788 4.659-4.788 1.325 0 2.463.099 2.797.143v3.24l-1.918.001c-1.504 0-1.797.715-1.797 1.763v2.313h3.587l-.467 3.622h-3.12V24h6.116C23.406 24 24 23.408 24 22.674V1.326C24 .592 23.406 0 22.675 0z"
        />
      </svg>
    ),
  },
];

export default function LoginForm({
  role,
  title,
  submitText,
  redirectPath,
  apiRoute,
  showOAuthProviders = false,
  showRememberMe = false,
  onLogin,
}: LoginFormProps) {
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const origin = searchParams.get('origin') || '';

  // Handle regular form login
  async function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    setError('');
    setLoading(true);

    const formData = new FormData(e.currentTarget);

    try {
      // Log the login attempt for analytics
      console.info(
        `Login attempt for ${role} from ${origin || 'direct access'}`
      );

      // For debugging - log the API route being used
      console.info(`Using API route: ${apiRoute}`);

      const res = await fetch(apiRoute, {
        method: 'POST',
        body: formData,
        headers: {
          'X-Login-Role': role,
          'X-Login-Origin': origin || 'direct',
        },
      });

      // For debugging - log the response status
      console.info(`Login response status: ${res.status}`);

      const data = await res.json();
      console.info('Login response:', data);

      if (data.success) {
        // Callback for any additional processing
        if (onLogin) onLogin(true);

        console.info(
          `Authentication successful, redirecting to ${redirectPath}`
        );

        // For admin login, we need to reload to apply the cookie
        if (role === 'admin') {
          // Small delay to ensure cookie is set
          setTimeout(() => {
            window.location.href = redirectPath;
          }, 500);
        } else {
          // Regular NextAuth redirect for users
          router.push(redirectPath);
        }
      } else {
        const errorMessage = data.error || 'Authentication failed';
        console.error('Authentication failed:', errorMessage);

        // Show toast error notification
        toast.error(errorMessage, {
          duration: 5000,
          position: 'bottom-right',
        });

        // Also set form error for inline display
        setError(errorMessage);
        if (onLogin) onLogin(false);
      }
    } catch (err) {
      const errorMessage = String(
        err instanceof Error ? err.message : 'Unknown error occurred'
      );
      console.error('Login error:', err);

      // Show toast error notification
      toast.error(`Login failed: ${errorMessage}`, {
        duration: 5000,
        position: 'bottom-right',
      });

      // Also set form error for inline display
      setError('An error occurred during login. Please try again.');
      if (onLogin) onLogin(false);
    } finally {
      setLoading(false);
    }
  }

  // Handle OAuth login
  async function handleOAuthLogin(providerId: string) {
    setError('');
    setLoading(true);

    try {
      // Import dynamically to avoid SSR issues
      const { signIn } = await import('next-auth/react');
      await signIn(providerId, {
        callbackUrl: redirectPath,
        redirect: true,
      });

      // Note: No need to call onLogin here as the page will redirect
    } catch (err) {
      const errorMessage = `Failed to authenticate with ${providerId}`;

      // Show toast notification
      toast.error(errorMessage, {
        duration: 5000,
        position: 'bottom-right',
      });

      // Also set form error
      setError(errorMessage);
      console.error('OAuth login error:', err);
      if (onLogin) onLogin(false);
      setLoading(false);
    }
  }

  return (
    <div className="mx-auto max-w-md px-4 py-16">
      <div className="rounded-xl border border-gray-200 bg-white shadow-sm dark:border-neutral-700 dark:bg-neutral-900">
        <div className="p-4 sm:p-7">
          <div className="text-center">
            <h1 className="block text-2xl font-bold text-gray-800 dark:text-white">
              {title}
            </h1>
            {error && <div className="mt-2 text-sm text-red-600">{error}</div>}
          </div>

          <div className="mt-5">
            {/* OAuth Providers */}
            {showOAuthProviders && (
              <>
                <div className="space-y-4">
                  {oauthProviders.map((provider) => (
                    <button
                      key={provider.id}
                      onClick={() => handleOAuthLogin(provider.id)}
                      className={`flex w-full items-center justify-center gap-3 rounded-lg px-4 py-3 text-base font-semibold text-white shadow transition-all hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 ${provider.color}`}
                    >
                      {provider.icon}
                      Sign in with {provider.name}
                    </button>
                  ))}
                </div>
                <div className="flex items-center py-3 text-xs uppercase text-gray-400 before:me-6 before:flex-1 before:border-t before:border-gray-200 after:ms-6 after:flex-1 after:border-t after:border-gray-200 dark:text-neutral-500 dark:before:border-neutral-600 dark:after:border-neutral-600">
                  Or
                </div>
              </>
            )}

            {/* Username/Password Login Form */}
            <form onSubmit={handleSubmit}>
              <div className="grid gap-y-4">
                <div>
                  <label
                    htmlFor="username"
                    className="mb-2 block text-sm dark:text-white"
                  >
                    Username
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      id="username"
                      name="username"
                      required
                      autoComplete="username"
                      className="block w-full rounded-lg border border-gray-300 px-4 py-2.5 text-sm focus:border-blue-500 focus:ring-blue-500 disabled:pointer-events-none disabled:opacity-50 sm:py-3 dark:border-neutral-700 dark:bg-neutral-900 dark:text-neutral-400 dark:placeholder:text-neutral-500 dark:focus:ring-neutral-600"
                    />
                  </div>
                </div>

                <div>
                  <div className="flex flex-wrap items-center justify-between gap-2">
                    <label
                      htmlFor="password"
                      className="mb-2 block text-sm dark:text-white"
                    >
                      Password
                    </label>
                  </div>
                  <div className="relative">
                    <input
                      type="password"
                      id="password"
                      name="password"
                      required
                      autoComplete="current-password"
                      className="block w-full rounded-lg border border-gray-300 px-4 py-2.5 text-sm focus:border-blue-500 focus:ring-blue-500 disabled:pointer-events-none disabled:opacity-50 sm:py-3 dark:border-neutral-700 dark:bg-neutral-900 dark:text-neutral-400 dark:placeholder:text-neutral-500 dark:focus:ring-neutral-600"
                    />
                  </div>
                </div>

                {showRememberMe && (
                  <div className="flex items-center">
                    <div className="flex">
                      <input
                        id="remember-me"
                        name="remember-me"
                        type="checkbox"
                        className="mt-0.5 shrink-0 rounded-sm border-gray-600 text-blue-600 focus:ring-blue-500 dark:border-neutral-700 dark:bg-neutral-800 dark:checked:border-blue-500 dark:checked:bg-blue-500 dark:focus:ring-offset-gray-800"
                      />
                    </div>
                    <div className="ms-3">
                      <label
                        htmlFor="remember-me"
                        className="text-sm dark:text-white"
                      >
                        Remember me
                      </label>
                    </div>
                  </div>
                )}

                <button
                  type="submit"
                  disabled={loading}
                  className="flex w-full items-center justify-center rounded-lg border border-transparent bg-blue-600 px-4 py-3 text-sm font-medium text-white hover:bg-blue-700 focus:bg-blue-700 focus:outline-none disabled:pointer-events-none disabled:opacity-50"
                >
                  {loading ? 'Signing in...' : submitText}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
