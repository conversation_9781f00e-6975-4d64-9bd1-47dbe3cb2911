'use client';
import CategoryCardClient from '@/components/CategoryCardClient';

interface BuilderHallStat {
  bhLevel: number;
  count: number;
  views: number;
}

interface HomeBuilderHallSectionProps {
  stats: BuilderHallStat[];
}

const HomeBuilderHallSection = ({ stats }: HomeBuilderHallSectionProps) => {
  return (
    <div className="grid grid-cols-2 gap-6 sm:grid-cols-3 md:grid-cols-4">
      {stats.map(({ bhLevel, count, views }) => (
        <CategoryCardClient
          key={bhLevel}
          type="BUILDER_HALL"
          level={bhLevel}
          count={count}
          views={views}
          href={`/base/builder-hall/${bhLevel}`}
          label={`BH${bhLevel} Bases (Builder Hall ${bhLevel})`}
          baseLabel="base"
        />
      ))}
    </div>
  );
};

export default HomeBuilderHallSection;
