'use client';
/**
 * @deprecated Use CategoryViewManager instead
 */

import { CategoryViewManager } from './CategoryViewManager';

interface CategoryViewTrackerProps {
  type: string;
  level: number;
}

export function CategoryViewTracker({ type, level }: CategoryViewTrackerProps) {
  console.warn(
    'CategoryViewTracker is deprecated. Use CategoryViewManager instead.'
  );

  // Just return an invisible component for backward compatibility
  return <CategoryViewManager type={type} level={level} initialVisits={0} />;
}
