'use client';
import { useEffect, useState } from 'react';

import { useViewTracker } from '@/hooks/useViewTracker';
import { createTimeoutSignal, formatViewCount } from '@/lib/utils';

interface CategoryViewManagerProps {
  type: string;
  level: number;
  initialVisits: number;
  displayOnly?: boolean; // If true, don't track, just display
}

export function CategoryViewManager({
  type,
  level,
  initialVisits,
  displayOnly = false,
}: CategoryViewManagerProps) {
  const [visits, setVisits] = useState(initialVisits);
  const [isHydrated, setIsHydrated] = useState(false);
  const { hasBeenViewed, markAsViewed } = useViewTracker();

  // Mark component as hydrated after initial render
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // Effect to handle tracking and display
  useEffect(() => {
    // Don't do anything if required props are missing or in display-only mode
    if (!type || typeof level !== 'number' || displayOnly) return;

    // Don't fetch until after hydration to prevent UI flicker
    if (!isHydrated) return;

    const trackingKey = `category:${type}:${level}`;
    let isMounted = true;

    // Add debounce to prevent too many fetches
    const lastFetchKey = `last_fetch:${trackingKey}`;
    const lastFetch = sessionStorage.getItem(lastFetchKey);
    const now = Date.now();

    // Skip fetch if we fetched within the last 30 seconds
    const shouldFetch = !lastFetch || now - parseInt(lastFetch, 10) > 30000;

    // Create a stable fetch function with appropriate error handling and retries
    async function fetchLatestCount() {
      // Don't fetch if we recently fetched (debounce)
      if (!shouldFetch) return;

      // Track the fetch time
      try {
        sessionStorage.setItem(lastFetchKey, now.toString());
      } catch {
        // Ignore storage errors
      }

      // Use retry pattern with exponential backoff
      let retries = 0;
      const maxRetries = 3;

      while (retries < maxRetries) {
        try {
          // Add both cache-busting timestamp and random value for complete cache prevention
          const timestamp = new Date().getTime();
          const randomVal = Math.random().toString(36).substring(2, 15);
          const res = await fetch(
            `/api/visits/category/views?type=${type}&level=${level}&_=${timestamp}&r=${randomVal}`,
            {
              // Strict no-cache settings to ensure fresh data
              cache: 'no-store',
              headers: {
                Pragma: 'no-cache',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
              },
              next: { revalidate: 0 },
              // Add timeout to prevent hanging requests (increased from 3s to 8s)
              signal: createTimeoutSignal(8000),
            }
          );

          // If rate limited, back off and retry
          if (res.status === 429) {
            retries++;
            const backoffTime = Math.min(1000 * Math.pow(2, retries), 8000);
            await new Promise((r) => setTimeout(r, backoffTime));
            continue;
          }

          if (!res.ok) throw new Error(`Failed to fetch count: ${res.status}`);

          const data = await res.json();
          if (isMounted && typeof data.views === 'number') {
            setVisits(data.views);
          }

          // Success, exit retry loop
          break;
        } catch (err) {
          retries++;
          if (retries >= maxRetries || !(err instanceof Error)) {
            console.warn('Failed to fetch category view count after retries');
            break;
          }

          // Don't retry on abort errors (timeouts)
          if (err.name === 'AbortError' || err.name === 'TimeoutError') {
            // Only log in development mode and use a debug level instead of warn
            if (process.env.NODE_ENV === 'development') {
              console.debug('View count fetch timed out - skipping retries');
            }
            break;
          }

          // Exponential backoff before retry
          const backoffTime = Math.min(1000 * Math.pow(2, retries), 8000);
          await new Promise((r) => setTimeout(r, backoffTime));
        }
      }
    }

    // Use a small delay before fetching to let the UI stabilize
    // This delay also helps avoid race conditions with the tracking API call
    const fetchTimer = setTimeout(() => {
      // Only fetch if component is still mounted and we should fetch
      if (isMounted) {
        fetchLatestCount().catch((e) => {
          // Catch and log any uncaught errors from the fetch process
          console.warn('Uncaught error in fetch process:', e);
        });
      }
    }, 500); // Increased to 500ms for better reliability

    // Only track if this is the first view in this session
    if (!hasBeenViewed(trackingKey)) {
      // Use an async IIFE for better error handling
      (async () => {
        try {
          // Mark as viewed first to prevent duplicate tracking across components
          const marked = markAsViewed(trackingKey);

          // Only make the API call if we successfully marked it as viewed
          if (marked) {
            const res = await fetch('/api/visits/category', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ type, level }),
              // Use cache: 'no-store' to prevent caching of this POST request
              cache: 'no-store',
            });

            if (!res.ok) throw new Error('Failed to increment');
            const data = await res.json();

            if (isMounted && typeof data.visits === 'number') {
              setVisits(data.visits);
            }
          }
        } catch (err) {
          // Optionally handle error
          if (process.env.NODE_ENV === 'development') {
            console.error(
              'Failed to increment category view',
              type,
              level,
              err
            );
          }
        }
      })();
    }

    return () => {
      isMounted = false;
      clearTimeout(fetchTimer);
    };
  }, [type, level, displayOnly, hasBeenViewed, markAsViewed, isHydrated]);

  return <span>{formatViewCount(visits)}</span>;
}
