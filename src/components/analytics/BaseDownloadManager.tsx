'use client';

import { useState } from 'react';
import { toast } from 'sonner';

import { incrementBaseDownloadCount } from '@/actions/base-download';

interface BaseDownloadManagerProps {
  baseId: string;
  initialDownloads: number;
}

export function BaseDownloadManager({
  baseId,
  initialDownloads,
}: BaseDownloadManagerProps) {
  const [downloads, setDownloads] = useState(initialDownloads);

  // Function to track download/open action
  const trackDownload = async () => {
    try {
      // Optimistic update
      setDownloads((prev) => prev + 1);

      // Update the database
      await incrementBaseDownloadCount(baseId);
    } catch (error) {
      // Revert optimistic update on error
      setDownloads((prev) => prev - 1);
      console.error('Failed to update download count:', error);
      toast.error('Failed to update download count');
    }
  };

  return (
    <>
      {downloads}
      {/* Expose the trackDownload function for use by parent components */}
      <span
        className="hidden"
        id={`download-tracker-${baseId}`}
        data-track-download={trackDownload}
      />
    </>
  );
}
