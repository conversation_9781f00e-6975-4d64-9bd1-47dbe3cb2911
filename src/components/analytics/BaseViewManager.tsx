'use client';
import { useEffect, useState } from 'react';

import { formatViewCount } from '@/lib/utils';

interface BaseViewManagerProps {
  baseId: string;
  initialViews: number;
  displayOnly?: boolean; // If true, don't track, just display
}

export function BaseViewManager({
  baseId,
  initialViews,
  displayOnly = false,
}: BaseViewManagerProps) {
  const [views, setViews] = useState(initialViews);
  const [isHydrated, setIsHydrated] = useState(false);

  // Mark component as hydrated after initial render
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  useEffect(() => {
    // Don't do anything if required props are missing or in display-only mode
    if (!baseId || displayOnly) return;

    // Don't fetch until after hydration to prevent UI flicker
    if (!isHydrated) return;

    let isMounted = true;

    // Create a stable fetch function with appropriate error handling
    async function fetchLatestCount() {
      try {
        // Add both cache-busting timestamp and random value for complete cache prevention
        const timestamp = new Date().getTime();
        const randomVal = Math.random().toString(36).substring(2, 15);
        const res = await fetch(
          `/api/visits/base/views?id=${baseId}&_=${timestamp}&r=${randomVal}`,
          {
            // Strict no-cache settings to ensure fresh data
            cache: 'no-store',
            headers: {
              Pragma: 'no-cache',
              'Cache-Control': 'no-cache, no-store, must-revalidate',
            },
            next: { revalidate: 0 },
          }
        );
        if (!res.ok) throw new Error('Failed to fetch count');

        const data = await res.json();
        if (isMounted && typeof data.views === 'number') {
          setViews(data.views);
        }
      } catch (err) {
        console.error('Failed to fetch base view count', err);
      }
    }

    // Always increment view count on every page load/refresh
    // Use an async IIFE for better error handling
    (async () => {
      try {
        const res = await fetch('/api/visits/base', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ id: baseId }),
          // Use cache: 'no-store' to prevent caching of this POST request
          cache: 'no-store',
        });

        if (!res.ok) throw new Error('Failed to increment');
        const data = await res.json();

        if (isMounted && typeof data.views === 'number') {
          setViews(data.views);
        }
      } catch (err) {
        // Optionally handle error
        if (process.env.NODE_ENV === 'development') {
          console.error('Failed to increment base view', baseId, err);
        }
      }
    })();

    // Use a small delay before fetching to let the UI stabilize
    // This ensures we get the freshest count after incrementing
    const fetchTimer = setTimeout(() => {
      fetchLatestCount();
    }, 500);

    return () => {
      isMounted = false;
      clearTimeout(fetchTimer);
    };
  }, [baseId, displayOnly, isHydrated]);

  return <span>{formatViewCount(views)}</span>;
}
