'use client';
/**
 * @deprecated Use BaseViewManager instead
 */

import { BaseViewManager } from './BaseViewManager';

interface BaseViewTrackerProps {
  baseId: string;
}

export function BaseViewTracker({ baseId }: BaseViewTrackerProps) {
  console.warn('BaseViewTracker is deprecated. Use BaseViewManager instead.');

  // Just return an invisible component for backward compatibility
  return <BaseViewManager baseId={baseId} initialViews={0} />;
}
