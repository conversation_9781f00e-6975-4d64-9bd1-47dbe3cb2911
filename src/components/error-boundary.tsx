'use client';

import { useEffect } from 'react';

interface ErrorBoundaryProps {
  error: Error;
  reset: () => void;
}

export function ErrorBoundary({ error, reset }: ErrorBoundaryProps) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Base page error:', error);
  }, [error]);

  return (
    <div className="flex h-[60vh] w-full flex-col items-center justify-center rounded-lg border border-red-200 bg-red-50 p-8 text-center">
      <svg
        className="mx-auto mb-4 size-12 text-red-400"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={1.5}
          d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"
        />
      </svg>
      <h2 className="mb-2 text-2xl font-semibold text-red-700">
        Something went wrong!
      </h2>
      <p className="mb-6 text-red-600">
        {error.message || "We're having trouble loading this page."}
      </p>
      <button
        onClick={reset}
        className="rounded-md bg-red-600 px-4 py-2 text-white transition-colors hover:bg-red-700"
      >
        Try again
      </button>
    </div>
  );
}
