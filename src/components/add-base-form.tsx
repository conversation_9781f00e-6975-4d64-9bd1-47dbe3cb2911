'use client';
import { useState } from 'react';

const TH_LEVELS = Array.from({ length: 10 }, (_, i) => 7 + i); // [7,8,...16]
const BASE_TYPES = ['War', 'Farming', 'Trophy', 'Hybrid'];

export default function AddBaseForm() {
  const [image, setImage] = useState<File | null>(null);
  const [title, setTitle] = useState('');
  const [link, setLink] = useState('');
  const [thLevel, setThLevel] = useState(7);
  const [type, setType] = useState(BASE_TYPES[0]);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    setSubmitting(true);
    setError(null);
    setSuccess(false);
    const formData = new FormData();
    if (image) formData.append('image', image);
    formData.append('title', title);
    formData.append('link', link);
    formData.append('thLevel', String(thLevel));
    formData.append('type', type);
    try {
      const res = await fetch('/api/base', {
        method: 'POST',
        body: formData,
      });
      if (!res.ok) throw new Error('Failed to add base');
      setSuccess(true);
      setTitle('');
      setLink('');
      setThLevel(7);
      setType(BASE_TYPES[0]);
      setImage(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setSubmitting(false);
    }
  }

  return (
    <form className="space-y-6" onSubmit={handleSubmit}>
      <div>
        <label htmlFor="image" className="mb-1 block font-medium">
          Image
        </label>
        <input
          id="image"
          type="file"
          accept="image/*"
          onChange={(e) => setImage(e.target.files?.[0] || null)}
          required
        />
      </div>
      <div>
        <label htmlFor="title" className="mb-1 block font-medium">
          Layout Title
        </label>
        <input
          id="title"
          className="input input-bordered w-full"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          required
        />
      </div>
      <div>
        <label htmlFor="link" className="mb-1 block font-medium">
          Clash of Clans Base Link
        </label>
        <input
          id="link"
          className="input input-bordered w-full"
          value={link}
          onChange={(e) => setLink(e.target.value)}
          required
        />
      </div>
      <div>
        <label htmlFor="thLevel" className="mb-1 block font-medium">
          Town Hall Level
        </label>
        <select
          id="thLevel"
          className="select select-bordered w-full"
          value={thLevel}
          onChange={(e) => setThLevel(Number(e.target.value))}
        >
          {TH_LEVELS.map((lvl) => (
            <option key={lvl} value={lvl}>
              TH{lvl}
            </option>
          ))}
        </select>
      </div>
      <fieldset className="mb-1 block font-medium">
        <legend className="mb-1">Base Type</legend>
        <div className="flex gap-4">
          {BASE_TYPES.map((t) => (
            <label
              key={t}
              className="flex items-center gap-1"
              htmlFor={`baseType-${t}`}
            >
              <input
                type="radio"
                name="baseType"
                id={`baseType-${t}`}
                value={t}
                checked={type === t}
                onChange={() => setType(t)}
              />
              {t}
            </label>
          ))}
        </div>
      </fieldset>
      <button
        type="submit"
        className="btn btn-primary w-full"
        disabled={submitting}
      >
        {submitting ? 'Submitting...' : 'Add Base'}
      </button>
      {error && <div className="text-red-500">{error}</div>}
      {success && (
        <div className="text-green-600">Base added successfully!</div>
      )}
    </form>
  );
}
