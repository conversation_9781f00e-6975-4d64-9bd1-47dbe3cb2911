'use client';

import { useEffect, useState } from 'react';
import {
  <PERSON>,
  Bar<PERSON>hart,
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';

import { Card } from '@/components/ui/dashboard';

// Component for displaying statistics

// Define types for our data
interface VisitorMetric {
  title: string;
  value: string;
  change: string;
}

interface PageViewDataPoint {
  name: string;
  'Town Hall': number;
  'Builder Hall': number;
}

interface CategoryDataPoint {
  name: string;
  value: number;
}

interface HallLevelDataPoint {
  name: string;
  views: number;
}

export function StatsTab() {
  const [activeView, setActiveView] = useState('daily');
  const [pageViewData, setPageViewData] = useState<PageViewDataPoint[]>([]);
  const [categoryData, setCategoryData] = useState<CategoryDataPoint[]>([]);
  const [hallLevelData, setHallLevelData] = useState<HallLevelDataPoint[]>([]);
  const [visitorMetrics, setVisitorMetrics] = useState<VisitorMetric[]>([]);
  const [loading, setLoading] = useState({
    pageViews: true,
    categories: true,
    hallLevels: true,
    metrics: true,
  });

  // Fetch data based on selected time period
  useEffect(() => {
    const fetchPageViews = async () => {
      setLoading((prev) => ({ ...prev, pageViews: true }));
      try {
        const response = await fetch(
          `/api/stats/page-views?period=${activeView}`
        );
        const data = await response.json();
        setPageViewData(data);
      } catch (error) {
        console.error('Error fetching page views:', error);
        // Fall back to empty data
        setPageViewData([]);
      } finally {
        setLoading((prev) => ({ ...prev, pageViews: false }));
      }
    };

    fetchPageViews();
  }, [activeView]);

  // Fetch category data
  useEffect(() => {
    const fetchCategoryData = async () => {
      setLoading((prev) => ({ ...prev, categories: true }));
      try {
        const response = await fetch('/api/stats/categories');
        const data = await response.json();
        setCategoryData(data);
      } catch (error) {
        console.error('Error fetching category data:', error);
        setCategoryData([]);
      } finally {
        setLoading((prev) => ({ ...prev, categories: false }));
      }
    };

    fetchCategoryData();
  }, []);

  // Fetch hall level data
  useEffect(() => {
    const fetchHallLevelData = async () => {
      setLoading((prev) => ({ ...prev, hallLevels: true }));
      try {
        const response = await fetch('/api/stats/hall-levels');
        const data = await response.json();
        setHallLevelData(data);
      } catch (error) {
        console.error('Error fetching hall level data:', error);
        setHallLevelData([]);
      } finally {
        setLoading((prev) => ({ ...prev, hallLevels: false }));
      }
    };

    fetchHallLevelData();
  }, []);

  // Fetch visitor metrics
  useEffect(() => {
    const fetchVisitorMetrics = async () => {
      setLoading((prev) => ({ ...prev, metrics: true }));
      try {
        const response = await fetch('/api/stats/visitor-metrics');
        const data = await response.json();
        setVisitorMetrics(data.metrics || []);
      } catch (error) {
        console.error('Error fetching visitor metrics:', error);
        setVisitorMetrics([]);
      } finally {
        setLoading((prev) => ({ ...prev, metrics: false }));
      }
    };

    fetchVisitorMetrics();
  }, []);

  return (
    <div className="w-full space-y-8">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Page Statistics</h3>
        <div className="bg-secondary rounded-lg p-1">
          <button
            className={`rounded-md px-3 py-1 text-sm ${
              activeView === 'daily'
                ? 'bg-primary text-white'
                : 'text-muted-foreground'
            }`}
            onClick={() => setActiveView('daily')}
          >
            Daily
          </button>
          <button
            className={`rounded-md px-3 py-1 text-sm ${
              activeView === 'weekly'
                ? 'bg-primary text-white'
                : 'text-muted-foreground'
            }`}
            onClick={() => setActiveView('weekly')}
          >
            Weekly
          </button>
          <button
            className={`rounded-md px-3 py-1 text-sm ${
              activeView === 'monthly'
                ? 'bg-primary text-white'
                : 'text-muted-foreground'
            }`}
            onClick={() => setActiveView('monthly')}
          >
            Monthly
          </button>
        </div>
      </div>

      <Card className="p-6">
        <h4 className="mb-4 text-lg font-medium">Page Views Over Time</h4>
        <div className="h-[300px]">
          {loading.pageViews ? (
            <div className="flex size-full items-center justify-center">
              <div className="size-8 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600"></div>
            </div>
          ) : pageViewData.length > 0 ? (
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={pageViewData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="Town Hall"
                  stroke="#8884d8"
                  strokeWidth={2}
                />
                <Line
                  type="monotone"
                  dataKey="Builder Hall"
                  stroke="#82ca9d"
                  strokeWidth={2}
                />
              </LineChart>
            </ResponsiveContainer>
          ) : (
            <div className="flex size-full items-center justify-center">
              <p className="text-muted-foreground">No data available</p>
            </div>
          )}
        </div>
      </Card>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <Card className="p-6">
          <h4 className="mb-4 text-lg font-medium">
            Base Categories Popularity
          </h4>
          <div className="h-[300px]">
            {loading.categories ? (
              <div className="flex size-full items-center justify-center">
                <div className="size-8 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600"></div>
              </div>
            ) : categoryData.length > 0 ? (
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={categoryData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="value" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex size-full items-center justify-center">
                <p className="text-muted-foreground">No data available</p>
              </div>
            )}
          </div>
        </Card>

        <Card className="p-6">
          <h4 className="mb-4 text-lg font-medium">Views by Hall Level</h4>
          <div className="h-[300px]">
            {loading.hallLevels ? (
              <div className="flex size-full items-center justify-center">
                <div className="size-8 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600"></div>
              </div>
            ) : hallLevelData.length > 0 ? (
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={hallLevelData} layout="vertical">
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" />
                  <YAxis dataKey="name" type="category" width={50} />
                  <Tooltip />
                  <Bar dataKey="views" fill="#82ca9d" />
                </BarChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex size-full items-center justify-center">
                <p className="text-muted-foreground">No data available</p>
              </div>
            )}
          </div>
        </Card>
      </div>

      <Card className="p-6">
        <h4 className="mb-4 text-lg font-medium">Visitor Metrics</h4>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
          {loading.metrics ? (
            <div className="col-span-full flex h-20 items-center justify-center">
              <div className="size-8 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600"></div>
            </div>
          ) : visitorMetrics.length > 0 ? (
            visitorMetrics.map((metric, index) => (
              <MetricCard
                key={index}
                title={metric.title}
                value={metric.value}
                change={metric.change}
              />
            ))
          ) : (
            <div className="col-span-full flex h-20 items-center justify-center">
              <p className="text-muted-foreground">No metrics available</p>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}

function MetricCard({
  title,
  value,
  change,
}: {
  title: string;
  value: string;
  change: string;
}) {
  const isPositive = change.startsWith('+');

  return (
    <div className="bg-secondary/20 rounded-lg p-4">
      <p className="text-muted-foreground text-sm">{title}</p>
      <p className="mt-1 text-2xl font-bold">{value}</p>
      <p
        className={`mt-2 text-xs ${isPositive ? 'text-green-500' : 'text-red-500'}`}
      >
        {change} from last period
      </p>
    </div>
  );
}
