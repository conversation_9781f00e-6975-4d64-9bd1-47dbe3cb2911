'use client';

import Head from 'next/head';

import { siteConfig } from '@/lib/constant';

interface CustomHeadProps {
  title?: string;
  description?: string;
  imageUrl?: string;
  imageWidth?: number;
  imageHeight?: number;
  imageType?: string;
  path?: string;
}

export default function CustomHead({
  title = siteConfig.title(),
  description = siteConfig.description(),
  imageUrl = `${siteConfig.url()}/opengraph-image.png`,
  imageWidth = 1200,
  imageHeight = 630,
  imageType = 'image/png',
  path = '',
}: CustomHeadProps) {
  const url = `${siteConfig.url()}${path}`;

  return (
    <Head>
      {/* Primary Meta Tags */}
      <title>{title}</title>
      <meta name="title" content={title} />
      <meta name="description" content={description} />

      {/* Open Graph / Facebook */}
      <meta property="og:title" content={title} />
      <meta property="og:type" content="website" />
      <meta property="og:url" content={url} />
      <meta property="og:image" content={imageUrl} />
      <meta property="og:image:secure_url" content={imageUrl} />
      <meta property="og:image:alt" content="Clash of Clans Bases" />
      <meta property="og:image:width" content={imageWidth.toString()} />
      <meta property="og:image:height" content={imageHeight.toString()} />
      <meta property="og:image:type" content={imageType} />
      <meta property="og:description" content={description} />
      <meta property="og:site_name" content={title} />
      <meta property="og:locale" content="en_US" />

      {/* Twitter */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={imageUrl} />
      <meta name="theme-color" content="#0072b1" />
    </Head>
  );
}
