import { jwtVerify } from 'jose';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

const JWT_SECRET = process.env.JWT_SECRET || 'supersecretkey';

export async function middleware(request: NextRequest) {
  // Auth protection for /add-base and /add-builder-base
  const token = request.cookies.get('token')?.value;
  let isAdmin = false;
  if (token) {
    try {
      const secret = new TextEncoder().encode(JWT_SECRET);
      await jwtVerify(token, secret);
      isAdmin = true;
    } catch {
      isAdmin = false;
    }
  }
  const isAddBase = request.nextUrl.pathname.startsWith('/add-base');
  const isAddBuilderBase =
    request.nextUrl.pathname.startsWith('/add-builder-base');
  const isSignIn = request.nextUrl.pathname.startsWith('/sign-in');

  if ((isAddBase || isAddBuilderBase) && !isAdmin) {
    const signInUrl = new URL('/sign-in', request.url);
    return NextResponse.redirect(signInUrl);
  }
  if (isSignIn && isAdmin) {
    return NextResponse.redirect(new URL('/', request.url));
  }
  return NextResponse.next();
}

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
};
