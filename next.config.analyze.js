const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
});

const { paraglide } = require('@inlang/paraglide-next/plugin');

/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'avatars.githubusercontent.com',
      },
    ],
  },
};

module.exports = withBundleAnalyzer(
  paraglide({
    paraglide: {
      project: './project.inlang',
      outdir: './src/paraglide',
    },
    ...nextConfig,
  })
);
