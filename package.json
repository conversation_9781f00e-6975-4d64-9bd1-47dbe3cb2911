{"name": "next-starter", "version": "1.0.0", "private": true, "scripts": {"dev": "prisma generate && next dev --turbo", "build": "prisma generate && prisma db push && next build", "start": "next start", "preview": "next build && next start", "lint": "next lint", "lint:fix": "next lint --fix", "format:check": "prettier --check \"**/*.{ts,tsx,mdx}\" --cache", "format:write": "prettier --write \"**/*.{ts,tsx,mdx}\" --cache", "typecheck": "tsc --noEmit", "test": "jest", "test:watch": "jest --watchAll", "e2e": "playwright test", "e2e:ui": "playwright test --ui", "postinstall": "paraglide-js compile --project ./project.inlang --outdir ./src/paraglide"}, "dependencies": {"@auth/prisma-adapter": "^2.7.4", "@hookform/resolvers": "^3.10.0", "@inlang/paraglide-next": "0.7.9", "@next/bundle-analyzer": "^15.3.5", "@prisma/client": "^6.13.0", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.4", "@reduxjs/toolkit": "^2.8.2", "@stripe/stripe-js": "^5.5.0", "@t3-oss/env-nextjs": "^0.11.1", "@vercel/analytics": "^1.5.0", "axios": "^1.11.0", "cheerio": "^1.1.2", "class-variance-authority": "^0.7.1", "cloudinary": "^2.7.0", "clsx": "^2.1.1", "framer-motion": "^12.23.6", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.473.0", "next": "^14.2.3", "next-auth": "^5.0.0-beta.25", "next-themes": "^0.4.4", "prisma": "^6.13.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.54.2", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-use-measure": "^2.1.7", "recharts": "^3.1.0", "sonner": "^2.0.6", "stripe": "^17.5.0", "tailwind-merge": "^2.6.0", "uuid": "^11.1.0", "zod": "^3.24.1"}, "devDependencies": {"@inlang/paraglide-js": "1.11.8", "@playwright/test": "^1.49.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/jest": "^29.5.14", "@types/node": "^22.16.2", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@typescript-eslint/eslint-plugin": "^8.20.0", "@typescript-eslint/parser": "^8.20.0", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-config-next": "15.1.5", "eslint-config-prettier": "^10.0.1", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.2.2", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-tailwindcss": "^3.18.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "prettier": "^3.4.2", "shadcn-ui": "^0.9.5", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}}