#!/usr/bin/env node

/**
 * This script handles database schema updates for the Clash of Clans Bases application
 * Specifically addressing the rename of 'downloads' to 'downloadCount' in Base model
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const SCHEMA_PATH = path.join(__dirname, '..', 'prisma', 'schema.prisma');

// Helper for running commands
function runCommand(command, silent = false) {
  try {
    const options = silent ? { stdio: 'pipe' } : { stdio: 'inherit' };
    return execSync(command, options).toString();
  } catch (error) {
    console.error(`Command failed: ${command}`);
    console.error(error.message);
    return null;
  }
}

// Main function
async function updateSchema() {
  console.log('\n🔄 Starting database schema update...\n');

  // 1. Check environment
  if (!fs.existsSync(SCHEMA_PATH)) {
    console.error('❌ Prisma schema file not found!');
    process.exit(1);
  }

  // 2. Display current schema status
  console.log('📋 Current schema status:');
  runCommand('npx prisma format');

  // 3. Try pushing schema (this is non-destructive)
  console.log('\n🔼 Pushing schema changes to database...');
  const pushResult = runCommand('npx prisma db push --accept-data-loss');

  if (!pushResult) {
    console.log('❌ Push failed, falling back to direct SQL approach...');

    // 4. Try direct SQL approach as fallback
    try {
      // Create SQL migration file
      const sqlPath = path.join(__dirname, '..', 'prisma', 'rename_column.sql');
      fs.writeFileSync(
        sqlPath,
        'ALTER TABLE "Base" RENAME COLUMN "downloads" TO "downloadCount";'
      );

      console.log('📄 Created SQL migration file');
      console.log('💾 Executing SQL migration...');

      runCommand(
        `npx prisma db execute --file ${sqlPath} --schema ${SCHEMA_PATH}`
      );
    } catch (error) {
      console.error('❌ SQL migration failed:', error.message);
      console.log('⚠️ You may need to manually update your database schema');
    }
  } else {
    console.log('✅ Schema pushed successfully!');
  }

  // 5. Generate fresh Prisma client
  console.log('\n🔄 Regenerating Prisma client...');
  runCommand('npx prisma generate');

  console.log('\n✅ Schema update process completed.');
  console.log('🚀 Now restart your application and try again!');
}

// Run the update
updateSchema().catch(console.error);
