// This is a migration script to fix base category values
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function fixBaseCategories() {
  try {
    // First, let's see how many bases we have
    const totalBases = await prisma.base.count();
    console.log(`Total bases in database: ${totalBases}`);

    // Get all bases regardless of category
    const allBases = await prisma.base.findMany({
      select: { id: true, category: true },
    });

    console.log(`Retrieved ${allBases.length} bases`);
    console.log('Category distribution:');

    // Log the distribution of categories
    const categoryCount = {};
    for (const base of allBases) {
      const cat = base.category || 'NULL';
      categoryCount[cat] = (categoryCount[cat] || 0) + 1;
    }

    console.log(categoryCount);

    // Update all bases to have a proper category
    const updates = [];

    for (let i = 0; i < allBases.length; i++) {
      const base = allBases[i];
      updates.push(
        prisma.base.update({
          where: { id: base.id },
          data: {
            // Force setting all to OTHER to ensure they're valid enum values
            category: 'OTHER',
          },
        })
      );

      if (updates.length >= 100 || i === allBases.length - 1) {
        await Promise.all(updates);
        console.log(`Updated ${updates.length} bases`);
        updates.length = 0;
      }
    }

    console.log('All bases updated successfully');
  } catch (error) {
    console.error('Error updating bases:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixBaseCategories();
