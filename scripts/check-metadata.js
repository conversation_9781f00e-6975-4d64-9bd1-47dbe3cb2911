const fs = require('fs');
const path = require('path');

// Find and read the JSON metadata for the root page
try {
  const metadataPath = path.resolve('.next/server/app', 'page.js.meta');
  if (fs.existsSync(metadataPath)) {
    const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf-8'));
    console.log('Root page metadata:', metadata);
  } else {
    console.log('No metadata file found at', metadataPath);
  }
} catch (error) {
  console.error('Error reading metadata:', error);
}

// Let's also try to locate OpenGraph-related files in the .next directory
try {
  const files = fs.readdirSync('.next', { recursive: true });
  const ogFiles = files.filter(
    (file) =>
      typeof file === 'string' &&
      (file.includes('opengraph') || file.includes('metadata'))
  );
  console.log('Files potentially related to OpenGraph:', ogFiles);
} catch (error) {
  console.error('Error looking for OpenGraph files:', error);
}
