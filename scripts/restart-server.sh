#!/bin/bash

# Restart server script with database schema fix
# This script will:
# 1. Stop any running Next.js server
# 2. Update the Prisma schema
# 3. Push schema changes to the database
# 4. Regenerate the Prisma client
# 5. Restart the Next.js development server

echo "🛑 Stopping any running Next.js server..."
pkill -f "next dev" || true

echo "🔄 Updating database schema..."
npx prisma db push --accept-data-loss

echo "🔄 Regenerating Prisma client..."
npx prisma generate

echo "🚀 Restarting Next.js development server..."
npm run dev
