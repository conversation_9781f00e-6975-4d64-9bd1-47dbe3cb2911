// Simple script to fix the database column name issue
const { PrismaClient } = require('@prisma/client');
const { execSync } = require('child_process');

async function fixDatabase() {
  const prisma = new PrismaClient();

  console.log('🔍 Checking database schema...');

  try {
    // First, try to execute a simple query on the new field name to check if it exists
    try {
      await prisma.base.findFirst({
        select: { downloadCount: true },
      });
      console.log('✅ Field downloadCount already exists in the database.');
    } catch (error) {
      // If we get here, the downloadCount field likely doesn't exist
      console.log('❌ Field downloadCount not found in database.');
      console.warn(error.message);

      console.log(
        '🔄 Attempting to rename column from downloads to downloadCount...'
      );

      // Try the rename operation
      try {
        await prisma.$executeRawUnsafe(
          'ALTER TABLE "Base" RENAME COLUMN "downloads" TO "downloadCount";'
        );
        console.log('✅ Successfully renamed column!');
      } catch (renameError) {
        console.error('❌ Error renaming column:', renameError.message);
        console.log('🔄 Trying alternative fix approach...');

        // Push schema changes as an alternative
        try {
          console.log('Running prisma db push...');
          execSync('npx prisma db push --accept-data-loss', {
            stdio: 'inherit',
          });
          console.log('✅ Schema updated through db push.');
        } catch (pushError) {
          console.error(
            '❌ Failed to update schema through db push:',
            pushError.message
          );
          console.log('⚠️ You may need to fix this manually. Options:');
          console.log('   1. Run: npx prisma db push --accept-data-loss');
          console.log(
            '   2. Or, connect to your database and run SQL: ALTER TABLE "Base" RENAME COLUMN "downloads" TO "downloadCount";'
          );
        }
      }
    }

    // Regenerate client
    console.log('🔄 Regenerating Prisma client...');
    execSync('npx prisma generate', { stdio: 'inherit' });
    console.log('✅ Prisma client regenerated.');
  } catch (error) {
    console.error('❌ Unexpected error:', error.message);
  } finally {
    await prisma.$disconnect();
  }

  console.log('✅ Database fix script completed.');
  console.log('🚀 Now restart your Next.js server with: npm run dev');
}

fixDatabase();
