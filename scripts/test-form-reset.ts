/**
 * This script tests the form reset functionality in the add-base-form component.
 * It simulates a form submission and checks if all fields are properly reset.
 * Run this script with: npm run ts-node scripts/test-form-reset.ts
 */

// Simple assertion function
function assertEqual<T>(actual: T, expected: T, message: string): void {
  if (actual !== expected) {
    console.error(`❌ ${message}: expected ${expected}, got ${actual}`);
    throw new Error(`Assertion failed: ${message}`);
  } else {
    console.log(`✓ ${message}`);
  }
}

// Mock the form reset behavior to test the state management
const mockFormReset = (): void => {
  // Define form state class to simulate React state
  class FormState {
    type: string = 'TOWN_HALL';
    level: number = 3;
    image: Record<string, unknown> | null = null;
    preview: string | null = null;
    baseLink: string = '';
    category: string = 'WAR';

    // Simulate form reset function
    resetForm(): void {
      this.type = 'TOWN_HALL';
      this.level = 3;
      this.image = null;
      this.preview = null;
      this.baseLink = '';
      this.category = 'WAR';
    }
  }

  // Create an instance of our form state
  const formState = new FormState();

  console.log('Initial state:');
  console.log('Type:', formState.type);
  console.log('Level:', formState.level);
  console.log('Category:', formState.category);

  // Simulate form changes
  formState.type = 'BUILDER_HALL';
  formState.level = 5;
  formState.image = {}; // Mocked file object
  formState.preview = 'data:image/png;base64,abc123';
  formState.baseLink = 'https://example.com/base';
  formState.category = 'TROPHY';

  console.log('\nAfter changes:');
  console.log('Type changed to:', formState.type);
  console.log('Level changed to:', formState.level);
  console.log('Base link changed to:', formState.baseLink);
  console.log('Category changed to:', formState.category);

  // Simulate form reset
  formState.resetForm();

  console.log('\nAfter reset:');
  console.log('Type:', formState.type);
  console.log('Level:', formState.level);
  console.log('Category:', formState.category);

  // Check that all fields are properly reset
  try {
    assertEqual(formState.type, 'TOWN_HALL', 'Type reset');
    assertEqual(formState.level, 3, 'Level reset');
    assertEqual(formState.image, null, 'Image reset');
    assertEqual(formState.preview, null, 'Preview reset');
    assertEqual(formState.baseLink, '', 'Base link reset');
    assertEqual(formState.category, 'WAR', 'Category reset');
    console.log('\n✓ Form reset test passed');
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error('\n❌ Form reset test failed:', errorMessage);
  }
};

// Run the test
console.log('Running form reset simulation test...');
mockFormReset();
console.log('Test complete!');

// This is a simple test script, not a full test suite
console.log('Running form reset simulation test...');
mockFormReset();
console.log('Test complete!');
