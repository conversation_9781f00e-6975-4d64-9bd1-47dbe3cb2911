// This script will update the database schema to rename the field
const { PrismaClient } = require('@prisma/client');
const { execSync } = require('child_process');

async function main() {
  console.log('Starting database update...');

  try {
    // Direct SQL approach
    const prisma = new PrismaClient();

    // Try to use Prisma to execute raw SQL
    try {
      console.log('Attempting to rename column using Prisma raw query...');
      await prisma.$executeRawUnsafe(
        'ALTER TABLE "Base" RENAME COLUMN "downloads" TO "downloadCount"'
      );
      console.log('Column renamed successfully using Prisma raw query!');
    } catch (sqlError) {
      console.error('Error renaming column:', sqlError.message);
      console.log(
        'The error above is expected if the column was already renamed or does not exist.'
      );
    }

    // Now verify the schema matches what we expect
    try {
      console.log('\nRegenerating Prisma client...');
      execSync('npx prisma generate', { stdio: 'inherit' });
      console.log('Prisma client regenerated successfully!');
    } catch (generateError) {
      console.error('Error regenerating Prisma client:', generateError.message);
    }

    console.log('\nDatabase update completed.');

    // Cleanup
    await prisma.$disconnect();
  } catch (error) {
    console.error('Failed to update database:', error);
    process.exit(1);
  }
}

main();
