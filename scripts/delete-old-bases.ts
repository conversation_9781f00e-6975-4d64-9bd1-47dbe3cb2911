#!/usr/bin/env ts-node
/**
 * This script deletes all bases from the database that were added on or before July 9, 2025
 *
 * Usage:
 * - Run this script with: npx ts-node scripts/delete-old-bases.ts
 * - Or with node: node -r ts-node/register scripts/delete-old-bases.ts
 * - Or directly: ./scripts/delete-old-bases.ts (after making executable with chmod +x)
 *
 * Options:
 * - --dry-run, -d     Run without actually deleting bases
 * - --yes, -y         Skip confirmation prompt
 * - --help, -h        Show help message
 * This script deletes all bases from the database that were added on or before July 9, 2025
 *
 * Usage:
 * - Run this script with: npx ts-node scripts/delete-old-bases.ts
 * - Or with node: node -r ts-node/register scripts/delete-old-bases.ts
 */

import prisma from '../src/lib/prisma';

async function deleteOldBases(dryRun = false) {
  // Create cutoff date: July 9, 2025, end of day (11:59:59 PM)
  const cutoffDate = new Date('2025-07-09T23:59:59Z');

  if (dryRun) {
    console.log(
      `DRY RUN: This will simulate deletion of bases added on or before ${cutoffDate.toISOString().split('T')[0]}`
    );
  } else {
    console.log(
      `Starting to delete bases added on or before ${cutoffDate.toISOString().split('T')[0]}...`
    );
  }

  try {
    // First, count how many bases will be deleted
    const count = await prisma.base.count({
      where: {
        createdAt: {
          lte: cutoffDate,
        },
      },
    });

    console.log(`Found ${count} bases to delete.`);

    // Ask for confirmation before proceeding
    if (!process.env.SKIP_CONFIRMATION) {
      console.log(
        '\n⚠️  WARNING: This action will permanently delete data and cannot be undone.'
      );
      console.log(
        'Type "CONFIRM" to proceed with deletion, or press Ctrl+C to cancel:'
      );

      const readline = require('readline').createInterface({
        input: process.stdin,
        output: process.stdout,
      });

      const confirmation = await new Promise<string>((resolve) => {
        readline.question('> ', (answer: string) => {
          readline.close();
          resolve(answer.trim());
        });
      });

      if (confirmation !== 'CONFIRM') {
        console.log('Deletion cancelled. No bases were deleted.');
        process.exit(0);
      }
    }

    // List the first few bases that will be deleted
    if (count > 0) {
      const basesToDelete = await prisma.base.findMany({
        where: {
          createdAt: {
            lte: cutoffDate,
          },
        },
        select: {
          id: true,
          type: true,
          level: true,
          createdAt: true,
          category: true,
        },
        take: 5, // Show up to 5 examples
      });

      console.log('\nExample bases to be deleted:');
      basesToDelete.forEach(
        (base: {
          id: string;
          type: string;
          level: number;
          createdAt: Date | string;
          category: string;
        }) => {
          const createdAt =
            base.createdAt instanceof Date
              ? base.createdAt.toISOString().split('T')[0]
              : new Date(base.createdAt).toISOString().split('T')[0];
          console.log(
            `- ID: ${base.id}, Type: ${base.type}, Level: ${base.level}, Created: ${createdAt}, Category: ${base.category}`
          );
        }
      );

      if (count > 5) {
        console.log(`...and ${count - 5} more bases`);
      }
    }

    if (dryRun) {
      console.log(`\nDRY RUN: Would have deleted ${count} bases.`);
    } else {
      // Perform the actual deletion
      const result = await prisma.base.deleteMany({
        where: {
          createdAt: {
            lte: cutoffDate,
          },
        },
      });

      console.log(`\nSuccessfully deleted ${result.count} bases.`);
    }

    // Update stats after deletion
    const remainingBases = await prisma.base.count();
    const townHallCount = await prisma.base.count({
      where: { type: 'TOWN_HALL' },
    });
    const builderHallCount = await prisma.base.count({
      where: { type: 'BUILDER_HALL' },
    });

    console.log('\nCurrent database stats:');
    console.log(`- Total remaining bases: ${remainingBases}`);
    console.log(`- Town Hall bases: ${townHallCount}`);
    console.log(`- Builder Hall bases: ${builderHallCount}`);
  } catch (error) {
    console.error('Error deleting bases:', error);
    process.exit(1);
  }
}

// Parse command line arguments
function parseArgs() {
  const args = process.argv.slice(2);
  const options = {
    dryRun: false,
    skipConfirmation: false,
  };

  for (const arg of args) {
    if (arg === '--dry-run' || arg === '-d') {
      options.dryRun = true;
    } else if (arg === '--yes' || arg === '-y') {
      options.skipConfirmation = true;
    } else if (arg === '--help' || arg === '-h') {
      console.log(`
Usage: npx ts-node scripts/delete-old-bases.ts [options]

Options:
  --dry-run, -d     Run without actually deleting bases
  --yes, -y         Skip confirmation prompt
  --help, -h        Show this help message
      `);
      process.exit(0);
    }
  }

  return options;
}

// Get command line options
const options = parseArgs();

// Set environment variable for confirmation skipping
if (options.skipConfirmation) {
  process.env.SKIP_CONFIRMATION = 'true';
}

// Run the function
deleteOldBases(options.dryRun)
  .then(() => {
    console.log('Script completed successfully.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });
