import { BaseCategory, BaseType, PrismaClient } from '@prisma/client';
import axios from 'axios';
import * as cheerio from 'cheerio';

const prisma = new PrismaClient();

const SOURCE_URL =
  'https://clashofclans-baselinks.com/th15-base-link/th15-legend-league/';
const LEVEL = 15;

const TYPE: BaseType = 'TOWN_HALL'; // or 'BUILDER_HALL' if scraping builder bases
const CATEGORY: BaseCategory = 'TROPHY';

async function main() {
  // Fetch first page to determine total pages
  let imported = 0;
  const importedBases: {
    imageUrl: string;
    baseLink: string;
    level: number;
    type: BaseType;
    category: BaseCategory;
    title: string | null;
  }[] = [];
  let html = '';
  try {
    const { data } = await axios.get(SOURCE_URL);
    html = data;
  } catch {
    console.error('Failed to fetch first page.');
    return;
  }
  const $ = cheerio.load(html);
  // Find last page number from pagination
  let lastPage = 1;
  $('.uagb-post-pagination-wrap a.page-numbers').each((_, el) => {
    const pageNum = parseInt($(el).text().trim(), 10);
    if (!isNaN(pageNum) && pageNum > lastPage) {
      lastPage = pageNum;
    }
  });
  // Also check for current page span
  $('.uagb-post-pagination-wrap span.page-numbers').each((_, el) => {
    const pageNum = parseInt($(el).text().trim(), 10);
    if (!isNaN(pageNum) && pageNum > lastPage) {
      lastPage = pageNum;
    }
  });
  console.log(`Detected ${lastPage} pages.`);
  for (let page = 1; page <= lastPage; page++) {
    let pageUrl = SOURCE_URL;
    if (page > 1) {
      pageUrl = SOURCE_URL.replace(/\/$/, '') + `/page/${page}/`;
    }
    console.log(`Scraping page ${page}: ${pageUrl}`);
    let pageHtml = '';
    try {
      const { data } = await axios.get(pageUrl);
      pageHtml = data;
    } catch {
      console.warn(`Failed to fetch page: ${pageUrl}`);
      continue;
    }
    const $$ = cheerio.load(pageHtml);
    const baseArticles = $$('article.uagb-post__inner-wrap');
    console.log(`Found ${baseArticles.length} articles on page ${page}.`);
    if (baseArticles.length === 0) {
      console.log(`No articles found on page ${page}.`);
      continue;
    }
    for (let i = 0; i < baseArticles.length; i++) {
      const article = baseArticles.eq(i);
      // Get image URL
      const img = article.find('.uagb-post__image img');
      let imageUrl = img.attr('src') || '';
      if (imageUrl && !imageUrl.startsWith('http')) {
        imageUrl = new URL(imageUrl, pageUrl).toString();
      }
      // Get detail page URL
      const detailLink = article.find('.uagb-post__image a').attr('href');
      if (!detailLink) continue;
      let detailUrl = detailLink;
      if (!detailUrl.startsWith('http')) {
        detailUrl = new URL(detailUrl, pageUrl).toString();
      }

      // Fetch detail page
      console.log(`  Fetching detail page: ${detailUrl}`);
      let detailHtml = '';
      try {
        const { data } = await axios.get(detailUrl);
        detailHtml = data;
      } catch {
        console.error('Failed to fetch detail page:', detailUrl);
        continue;
      }
      const $detail = cheerio.load(detailHtml);
      // Find the base link (Copy this layout)
      let baseLink = '';
      $detail('a').each((_, el) => {
        const href = $detail(el).attr('href');
        if (href && href.includes('link.clashofclans.com')) {
          baseLink = href;
        }
      });
      if (!baseLink) {
        console.warn('No base link found for', detailUrl);
        continue;
      }
      // Extract correct image from detail page: if 2 images, use 2nd; if 3+, use last; if 1, use that
      const detailImgs = $detail('.wp-block-uagb-image img');
      console.log(`    Found ${detailImgs.length} images in detail page.`);
      let chosenImg = '';
      if (detailImgs.length === 1) {
        chosenImg = detailImgs.eq(0).attr('src') || '';
      } else if (detailImgs.length === 2) {
        chosenImg = detailImgs.eq(1).attr('src') || '';
      } else if (detailImgs.length > 2) {
        chosenImg = detailImgs.eq(detailImgs.length - 1).attr('src') || '';
      }
      if (chosenImg && chosenImg.startsWith('http')) {
        imageUrl = chosenImg;
      }
      // Extract title
      let title = $detail('h2.wp-block-heading').first().text().trim() || null;
      if (!title) {
        title = $detail('title').text().trim() || null;
      }
      // Skip duplicates
      const exists = await prisma.base.findFirst({ where: { baseLink } });
      if (exists) continue;
      // Validate required fields for Base model
      const isValid =
        typeof imageUrl === 'string' &&
        imageUrl.length > 0 &&
        typeof baseLink === 'string' &&
        baseLink.length > 0 &&
        typeof LEVEL === 'number' &&
        LEVEL > 0 &&
        typeof TYPE === 'string' &&
        TYPE.length > 0 &&
        typeof CATEGORY === 'string' &&
        CATEGORY.length > 0 &&
        typeof title === 'string' &&
        title.length > 0;
      if (!isValid) {
        console.warn('Skipped invalid base:', {
          imageUrl,
          baseLink,
          level: LEVEL,
          type: TYPE,
          category: CATEGORY,
          title,
        });
        continue;
      }
      // Insert into Prisma
      await prisma.base.create({
        data: {
          imageUrl,
          baseLink,
          level: LEVEL,
          type: TYPE,
          category: CATEGORY,
          title,
        },
      });
      importedBases.push({
        imageUrl,
        baseLink,
        level: LEVEL,
        type: TYPE,
        category: CATEGORY,
        title,
      });
      imported++;
      console.log(`Imported: ${title || baseLink}`);
    }
  }
  console.log(`Done. Imported ${imported} new bases.`);
  console.log('Imported base data:');
  console.log(JSON.stringify(importedBases, null, 2));
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(() => prisma.$disconnect());
