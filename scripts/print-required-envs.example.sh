#!/bin/bash
# Example script for required environment variables (NO REAL CREDENTIALS)
# Make a copy of this file named print-required-envs.sh and fill in your values

cat <<EOVARS
DATABASE_URL="postgresql://username:password@host:port/database"
AUTH_USERNAME='your_username'
AUTH_EMAIL='<EMAIL>'
AUTH_PASSWORD='your_secure_password'
JWT_SECRET=your_jwt_secret_here
APP_URL='https://your-app-url.com'
GITHUB_ID='your_github_id'
GITHUB_SECRET='your_github_secret'
GOOGLE_CLIENT_ID=your_google_client_id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your_google_client_secret
FACEBOOK_CLIENT_ID=your_facebook_client_id
FACEBOOK_CLIENT_SECRET=your_facebook_client_secret
NEXTAUTH_SECRET='your_nextauth_secret'
NEXTAUTH_URL='http://localhost:3000'
EOVARS

echo "\nFill in the values above and copy them to your Vercel dashboard (Settings → Environment Variables)."
