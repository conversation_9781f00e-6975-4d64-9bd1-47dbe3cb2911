#!/usr/bin/env ts-node

/**
 * This script deletes ALL bases from the database without confirmation
 * With timeout handling to prevent hanging
 */

import { PrismaClient } from '@prisma/client';

// Create a new PrismaClient with timeout
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
});

// Set a timeout to kill the process if it takes too long
const TIMEOUT_MS = 10000; // 10 seconds
const timeout = setTimeout(() => {
  console.error('Operation timed out after 10 seconds!');
  console.error(
    'The database may be unreachable or the operation is taking too long.'
  );
  process.exit(1);
}, TIMEOUT_MS);

// Clear the timeout when done
function clearTimeoutSafely() {
  clearTimeout(timeout);
}

async function deleteAllBases() {
  console.log('Starting deletion of ALL bases from the database...');

  try {
    console.log('Connecting to database...');

    // Use a raw query for faster execution and to avoid ORM overhead
    console.log('Executing direct SQL query to delete all bases...');
    const result = await prisma.$executeRaw`DELETE FROM "Base"`;

    console.log(`\nSuccessfully deleted ${result} bases.`);
    clearTimeoutSafely();

    // Disconnect from Prisma to allow the script to exit
    await prisma.$disconnect();

    return result;
  } catch (error) {
    clearTimeoutSafely();
    console.error('Error deleting bases:', error);

    // Make sure to disconnect even on error
    try {
      await prisma.$disconnect();
    } catch (disconnectError) {
      // Ignore disconnect errors
      console.error('Error disconnecting from database:', disconnectError);
    }

    process.exit(1);
  }
}

// Run the function immediately without confirmation
console.log('⚠️ WARNING: Deleting ALL bases without confirmation!');
deleteAllBases()
  .then((count) => {
    console.log(`Database cleanup completed. ${count || 'All'} bases deleted.`);
    process.exit(0);
  })
  .catch((error) => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });
