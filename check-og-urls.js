// This is a simple script to check if the OpenGraph URLs are correctly generated
// Run this with `node check-og-urls.js`

const { env } = require('./src/env.mjs');

console.log('Current APP_URL:', env.APP_URL);
console.log('OpenGraph image URL:', `${env.APP_URL}/opengraph-image.png`);

// Verify that the APP_URL is an absolute URL that includes the protocol
if (!env.APP_URL.startsWith('http')) {
  console.error('WARNING: APP_URL should start with http:// or https://');
} else {
  console.log('APP_URL format is correct');
}
